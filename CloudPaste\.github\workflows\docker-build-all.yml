name:  <PERSON>uild and Push all Docker Image

on:
  workflow_dispatch:
    inputs:
      version:
        description: "自定义版本标签(不包含v前缀)"
        required: false
        default: ""

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.set-version.outputs.version }}
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置版本
        id: set-version
        run: |
          # 1. 首先检查是否有手动指定的版本
          if [[ "${{ github.event.inputs.version }}" != "" ]]; then
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
            exit 0
          fi

          # 2. 使用短提交哈希作为版本（因为没有标签推送触发）
          SHORT_SHA=$(git rev-parse --short HEAD)
          echo "version=dev-${SHORT_SHA}" >> $GITHUB_OUTPUT

  build-backend:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置QEMU
        uses: docker/setup-qemu-action@v3

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 构建并推送后端镜像
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/backend/Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/cloudpaste-backend:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/cloudpaste-backend:${{ needs.setup.outputs.version }}
          platforms: linux/amd64,linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          labels: |
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}

  build-frontend:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置QEMU
        uses: docker/setup-qemu-action@v3

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 构建并推送前端镜像
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/frontend/Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/cloudpaste-frontend:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/cloudpaste-frontend:${{ needs.setup.outputs.version }}
          platforms: linux/amd64,linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          labels: |
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
