{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist", "buildCommand": "npm run vercel-build"}}], "env": {"VITE_APP_ENV": "production"}, "routes": [{"src": "/config.js", "headers": {"cache-control": "no-cache, no-store, must-revalidate"}, "continue": true}, {"src": "/assets/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "continue": true}, {"src": "/(.*).json", "headers": {"cache-control": "public, max-age=3600"}, "continue": true}, {"src": "/manifest.json", "headers": {"cache-control": "public, max-age=3600"}, "continue": true}, {"src": "/sw.js", "headers": {"cache-control": "no-cache, no-store, must-revalidate"}, "continue": true}, {"src": "/workbox-*.js", "headers": {"cache-control": "no-cache, no-store, must-revalidate"}, "continue": true}, {"src": "/icons/(.*)", "headers": {"cache-control": "public, max-age=2592000"}, "continue": true}, {"src": "/(.*)", "headers": {"x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-xss-protection": "1; mode=block"}, "continue": true}, {"handle": "filesystem"}, {"src": "/paste/.*", "dest": "/index.html"}, {"src": "/file/.*", "dest": "/index.html"}, {"src": "/admin", "dest": "/index.html"}, {"src": "/admin/.*", "dest": "/index.html"}, {"src": "/mount-explorer", "dest": "/index.html"}, {"src": "/mount-explorer/.*", "dest": "/index.html"}, {"src": "/upload", "dest": "/index.html"}, {"src": "/(.*)", "dest": "/index.html"}]}