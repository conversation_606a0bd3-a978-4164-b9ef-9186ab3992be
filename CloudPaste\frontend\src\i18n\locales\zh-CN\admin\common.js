export default {
  "login": {
    "adminLogin": "管理员登录",
    "apiKeyAuth": "API密钥认证",
    "username": "用户名",
    "password": "密码",
    "apiKey": "API密钥",
    "loginButton": "登录",
    "loggingIn": "登录中...",
    "useApiKey": "使用API密钥登录",
    "useAdminAccount": "使用管理员账户登录",
    "inputRequired": {
      "usernamePassword": "请输入用户名和密码",
      "apiKey": "请输入API密钥"
    },
    "errors": {
      "invalidToken": "无效的登录令牌",
      "invalidCredentials": "用户名或密码错误",
      "loginFailed": "登录失败",
      "invalidResponse": "服务器响应格式无效",
      "serverError": "服务器错误",
      "keyValidationFailed": "API密钥验证失败",
      "permissionInfo": "无法获取权限信息",
      "invalidApiKey": "无效或未授权的API密钥",
      "insufficientPermissions": "API密钥权限不足"
    }
  },
  "permissionDenied": {
    "title": "权限不足"
  },
  "title": {
    "admin": "管理面板",
    "user": "用户面板"
  },
  "sidebar": {
    "dashboard": "仪表板",
    "textManagement": "文本管理",
    "fileManagement": "文件管理",
    "storageConfig": "S3存储配置",
    "mountManagement": "挂载管理",
    "keyManagement": "密钥管理",
    "settings": "系统设置",
    "logout": "退出登录",
    "logoutAuth": "退出认证",
    "openMenu": "打开菜单",
    "closeMenu": "关闭菜单",
    "menuTitle": {
      "admin": "管理面板",
      "user": "用户面板"
    }
  }
};
