{"name": "cloudpaste-api", "version": "0.6.5", "description": "CloudPaste API基于Cloudflare Workers和D1数据库", "main": "worker.js", "scripts": {"dev": "wrangler dev --local", "deploy": "wrangler publish", "docker-dev": "cross-env RUNTIME_ENV=docker PORT=8787 DATA_DIR=./data node server.js", "docker-start": "cross-env RUNTIME_ENV=docker PORT=8787 DATA_DIR=/data node server.js"}, "keywords": ["cloudflare", "workers", "d1", "api", "paste"], "author": "", "license": "Apache-2.0", "type": "module", "dependencies": {"@aws-crypto/sha256-browser": "^5.2.0", "@aws-crypto/sha256-js": "^5.2.0", "@aws-sdk/client-s3": "3.726.1", "@aws-sdk/lib-storage": "3.726.1", "@aws-sdk/s3-request-presigner": "3.726.1", "cors": "^2.8.5", "express": "^4.18.2", "file-type": "^21.0.0", "hono": "^3.2.0", "method-override": "^3.0.0", "multer": "^1.4.5-lts.2"}, "devDependencies": {"cross-env": "^7.0.3", "wrangler": "^3.0.0"}, "optionalDependencies": {"sqlite": "^5.1.1", "sqlite3": "^5.1.6"}}