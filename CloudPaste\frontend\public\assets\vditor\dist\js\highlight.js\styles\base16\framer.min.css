/*!
  Theme: Framer
  Author: Framer (Maintained by <PERSON>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#d0d0d0;background:#181818}.hljs ::selection,.hljs::selection{background-color:#464646;color:#d0d0d0}.hljs-comment{color:#747474}.hljs-tag{color:#b9b9b9}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#d0d0d0}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#fd886b}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#fc4769}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#fecb6e}.hljs-strong{font-weight:700;color:#fecb6e}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#32ccdc}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#acddfd}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#20bcfc}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#ba8cfc}.hljs-emphasis{color:#ba8cfc;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b15f4a}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
