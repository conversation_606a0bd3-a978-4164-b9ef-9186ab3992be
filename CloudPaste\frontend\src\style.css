@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors duration-200;
  }

  .btn-secondary {
    @apply px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100 dark:focus:ring-primary-600 dark:focus:border-primary-600;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-colors duration-200 dark:bg-gray-800 dark:border-gray-700;
  }
}

/* 暗色模式样式通过 class 控制，而不是媒体查询 */
.dark {
  color-scheme: dark;
}

.dark body {
  @apply bg-gray-900 text-gray-100;
}

/* 移动设备优化 */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}
