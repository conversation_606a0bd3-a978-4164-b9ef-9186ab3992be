
/* 现代化图廊工具栏样式 - 优化为更低调的设计 */
.gallery-toolbar {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.dark .gallery-toolbar {
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.gallery-toolbar:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.dark .gallery-toolbar:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 工具栏按钮样式 - 移除过度动效 */
.gallery-toolbar button {
  font-weight: 500;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* 下拉菜单样式 - 简化阴影 */
.gallery-toolbar .absolute {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.dark .gallery-toolbar .absolute {
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* MasonryWall 瀑布流样式 */
.masonry-wall-gallery {
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* 瀑布流项目容器 */
.masonry-item {
  width: 100%;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
  /* 只控制垂直间距，水平间距由MasonryWall的gap属性控制 */
  margin-bottom: var(--vertical-gap, 20px);
}

.masonry-item:hover {
  transform: translateY(-2px);
}

/* 瀑布流图片容器 */
.masonry-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  width: 100%;
  cursor: pointer;
}

/* 图片包装器 */
.masonry-image-wrapper {
  width: 100%;
  position: relative;
}

.masonry-image-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 瀑布流图片样式 */
.masonry-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.masonry-image-container:hover .masonry-image {
  transform: scale(1.05);
}

/* 瀑布流悬浮信息层 */
.masonry-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(transparent 60%, rgba(0, 0, 0, 0.8));
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  pointer-events: none; /* 默认不接收鼠标事件 */
}

.masonry-image-container:hover .masonry-overlay {
  opacity: 1;
  pointer-events: auto; /* 悬浮时允许鼠标事件 */
}

/* 瀑布流图片信息 */
.masonry-info {
  padding: 12px;
  color: white;
  flex: 1;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.masonry-image-container:hover .masonry-info {
  transform: translateY(0);
}

/* 图片操作菜单样式 */
.masonry-overlay .absolute {
  pointer-events: auto; /* 确保操作按钮可以点击 */
}

/* 操作菜单按钮样式 */
.masonry-overlay button[data-menu-button] {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.masonry-overlay button[data-menu-button]:hover {
  transform: scale(1.05);
}

/* 操作菜单下拉样式 */
.masonry-overlay [data-menu-content] {
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeInScale 0.15s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 瀑布流占位符样式 */
.masonry-placeholder {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 2px dashed rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.05);
}

.placeholder-content {
  text-align: center;
}

/* 间距滑块样式 */
.spacing-slider {
  background: linear-gradient(to right, #e5e7eb 0%, #e5e7eb 100%);
  transition: all 0.2s ease;
}

.dark .spacing-slider {
  background: linear-gradient(to right, #374151 0%, #374151 100%);
}

.spacing-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.horizontal-slider::-webkit-slider-thumb {
  background: #10b981; /* 绿色表示水平 */
}

.vertical-slider::-webkit-slider-thumb {
  background: #3b82f6; /* 蓝色表示垂直 */
}

.dark .horizontal-slider::-webkit-slider-thumb {
  background: #34d399;
  border-color: #1f2937;
}

.dark .vertical-slider::-webkit-slider-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}

.spacing-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Firefox 滑块样式 */
.spacing-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.horizontal-slider::-moz-range-thumb {
  background: #10b981;
}

.vertical-slider::-moz-range-thumb {
  background: #3b82f6;
}

.dark .horizontal-slider::-moz-range-thumb {
  background: #34d399;
  border-color: #1f2937;
}

.dark .vertical-slider::-moz-range-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}

/* MasonryWall 自动处理响应式，无需媒体查询 */

/* 暗色模式适配 */
.dark .image-container {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark .image-placeholder {
  border-color: rgba(255, 255, 255, 0.1);
}
