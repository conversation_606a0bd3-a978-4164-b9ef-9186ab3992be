/*!
  Theme: Atelier Seaside Light
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#5e6e5e;background:#f4fbf4}.hljs ::selection,.hljs::selection{background-color:#8ca68c;color:#5e6e5e}.hljs-comment{color:#809980}.hljs-tag{color:#687d68}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#5e6e5e}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#e6193c}.<PERSON>ljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#87711d}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#98981b}.hljs-strong{font-weight:700;color:#98981b}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#29a329}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#1999b3}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#3d62f5}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#ad2bee}.hljs-emphasis{color:#ad2bee;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#e619c3}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
