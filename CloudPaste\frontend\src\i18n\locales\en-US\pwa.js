export default {
  pwa: {
    // Status indicator
    status: {
      title: "PWA Status Details",
      offline: "Offline Mode",
      updateAvailable: "Update Available",
      updating: "Updating",
      installable: "Installable",
      ready: "PWA Ready",
    },

    // Status labels
    labels: {
      networkStatus: "Network Status",
      installStatus: "Install Status",
      serviceWorker: "Service Worker",
      updateStatus: "Update Status",
      notificationPermission: "Notification Permission",
      backgroundSync: "Background Sync",
      appVersion: "App Version",
    },

    // Network status
    network: {
      online: "Online",
      offline: "Offline",
    },

    // Install status
    install: {
      installed: "Installed",
      notInstalled: "Not Installed",
      installable: "Installable",
      installing: "Installing",
      installApp: "Install App",
      installPrompt: "Add CloudPaste to your home screen for a better experience",
    },

    // Service Worker status
    serviceWorker: {
      unknown: "Unknown",
      installing: "Installing",
      waiting: "Waiting",
      active: "Active",
      redundant: "Redundant",
    },

    // Update status
    update: {
      latest: "Latest Version",
      available: "Update Available",
      updating: "Updating",
      updateApp: "Update Now",
      checkUpdate: "Check Update",
      checking: "Checking",
      updatePrompt: "New version available, click to update for latest features",
      updateSuccess: "Update Successful",
      updateFailed: "Update Failed",
    },

    // Notification permission
    notification: {
      default: "Not Set",
      granted: "Granted",
      denied: "Denied",
      enable: "Enable Notifications",
      requesting: "Requesting",
      requestFailed: "Failed to request notification permission",
    },

    // Background sync
    backgroundSync: {
      supported: "Supported",
      notSupported: "Not Supported",
      syncing: "Syncing",
      syncComplete: "Sync Complete",
      syncFailed: "Sync Failed",
    },

    // Offline messages
    offline: {
      message: "You are offline, some features may be limited",
      restored: "Network restored, syncing data...",
      pageUnavailable: "{page} is temporarily unavailable, please check your connection",
    },

    // Install prompt
    installPrompt: {
      title: "Install App",
      message: "Add CloudPaste to your home screen for a better experience",
      install: "Install",
      later: "Later",
      dismiss: "Later",
    },

    // Update prompt
    updatePrompt: {
      title: "New Version Available",
      message: "New version available, click to update for latest features",
      update: "Update",
      later: "Later",
      dismiss: "Later",
    },

    // Action buttons
    actions: {
      install: "Install",
      update: "Update",
      checkUpdate: "Check Update",
      enableNotification: "Enable Notifications",
      close: "Close",
      later: "Later",
      dismiss: "Later",
    },

    // Error messages
    errors: {
      installFailed: "Installation Failed",
      updateFailed: "Update Failed",
      notificationFailed: "Failed to request notification permission",
      syncFailed: "Data sync failed, please try again later",
      notSupported: "Your browser does not support this feature",
    },

    // Success messages
    success: {
      installed: "App installed successfully",
      updated: "App updated to latest version",
      notificationEnabled: "Notification permission enabled",
      syncComplete: "Data sync complete",
    },
  },
};
