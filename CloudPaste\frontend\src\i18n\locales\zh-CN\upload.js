export default {
  file: {
    uploadPageTitle: "文件上传",
    permissionRequired: "需要管理员权限或有效的API密钥才能上传文件，请",
    loginOrAuth: "登录管理后台或配置API密钥",
    uploadTabs: {
      fileUpload: "文件上传",
      urlUpload: "URL直链上传",
    },
    dragDropTitle: "拖拽文件到此处或点击选择",
    dragDropSubtitle: "支持多文件上传",
    dragDropHint: "最大文件大小: {size}",
    selectFiles: "选择文件",
    drag: "拖拽文件到此处",
    select: "拖拽文件到此处或点击选择",
    maxSizeExceeded: "最大文件大小: {size}",
    multipleFilesSupported: "支持多文件上传",
    selectedFiles: "已选择 {count} 个文件",
    clearAll: "清空全部",
    pending: "等待中",
    success: "成功",
    error: "失败",
    retry: "重试",
    clearSelected: "移除",
    cancelUpload: "取消上传",
    fileName: "文件名",
    fileSize: "大小",
    fileType: "类型",
    password: "密码",
    createdAt: "创建时间",
    actions: "操作",
    remainingViewsLabel: "剩余次数",
    unlimited: "无限制",
    usedUp: "已用完",
    unknownSize: "未知大小",
    form: {
      s3Config: "存储配置",
      selectS3Config: "选择存储配置",
      customSlug: "自定义链接",
      customSlugPlaceholder: "自定义文件访问链接（可选）",
      path: "存储路径",
      pathPlaceholder: "文件存储路径（可选）",
      remark: "备注",
      remarkPlaceholder: "为文件添加备注（可选）",
      password: "访问密码",
      passwordPlaceholder: "设置访问密码（可选）",
      expiryTime: "过期时间",
      expiryNever: "永不过期",
      expiryHour: "小时后过期",
      expiryDay: "天后过期",
      expiryWeek: "周后过期",
      expiryMonth: "月后过期",
      maxViews: "最大查看次数",
      maxViewsPlaceholder: "限制查看次数（0为不限制）",
      uploadButton: "开始上传",
      uploading: "上传中...",
      cancelUpload: "取消上传",
    },
    storage: "存储配置",
    selectStorage: "选择存储配置",
    noStorage: "暂无可用存储配置",
    path: "存储路径",
    pathPlaceholder: "文件存储路径（可选）",
    shareSettings: "分享设置",
    remark: "备注",
    remarkPlaceholder: "为文件添加备注（可选）",
    customLink: "自定义链接",
    customLinkPlaceholder: "自定义文件访问链接（可选）",
    passwordProtection: "密码保护",
    passwordPlaceholder: "设置访问密码（可选）",
    expireTime: "过期时间",
    maxViews: "最大查看次数",
    maxViewsPlaceholder: "限制查看次数（0为不限制）",
    onlyAllowedChars: "只能包含字母、数字、连字符和下划线",
    expireOptions: {
      hour1: "1小时后过期",
      day1: "1天后过期",
      day7: "7天后过期",
      day30: "30天后过期",
      never: "永不过期",
    },
    upload: "开始上传",
    loading: "加载中...",
    cancel: "取消",
    urlUpload: {
      urlInput: "文件URL",
      urlInputPlaceholder: "输入要上传的文件URL地址",
      analyzeUrl: "分析URL",
      analyzing: "分析中...",
      customFilename: "自定义文件名",
      customFilenamePlaceholder: "自定义文件名（可选）",
      filePreview: "文件预览",
      uploadFromUrl: "从URL上传",
      urlUpload: "URL上传",
      urlAnalysisComplete: "URL分析完成",
      retryAnalysis: "重新分析",
    },
    enterUrl: "输入文件URL地址",
    supportedUrlTypes: "支持HTTP和HTTPS链接",
    urlPlaceholder: "输入要上传的文件URL地址",
    analyze: "分析",
    analyzing: "分析中...",
    urlFileInfo: "文件信息",
    clear: "清除",
    customFileName: "自定义文件名",
    customFilename: "自定义文件名（可选）",
    uploadMethod: "上传方式",
    presignedUpload: "预签名上传",
    multipartUpload: "分片上传",
    presignedUploadDesc: "预签名URL直传到存储",
    multipartUploadDesc: "分片直传到存储",
    starting: "准备中...",
    downloading: "下载中...",
    downloadingProxy: "代理下载中...",
    preparing: "准备中...",
    initializing: "初始化...",
    uploading: "上传中...",
    finalizing: "完成中...",
    completed: "已完成",
    cancelled: "已取消",
    recentUploads: "最近上传",
    showingRecent: "显示最近3条记录",
    noFiles: "暂无文件",
    noFilesUploaded: "暂无上传文件",
    uploadToShow: "上传文件后将在此显示",
    open: "打开",
    delete: "删除",
    qrCode: "二维码",
    encrypted: "已加密",
    noPassword: "无密码",
    fileQrCode: "文件二维码",
    deleting: "删除中...",
    confirmDeleteBtn: "确认删除",
    deletedSuccess: "文件删除成功",
    qrCodeDownloadSuccess: "二维码下载成功",
    noValidLink: "无有效链接",
    cannotGetProxyLink: "无法获取代理链接",
    copyPermanentLinkFailed: "复制永久链接失败",
    getPasswordFromSessionError: "从会话存储获取密码失败",
    copyLink: "复制链接",
    copyDirectLink: "复制直链",
    downloadFile: "下载文件",
    deleteFile: "删除文件",
    showQRCode: "显示二维码",
    downloadQrCode: "下载二维码",
    uploadSuccessful: "文件上传成功",
    urlUploadSuccess: "URL文件上传成功",
    multipleUploadsSuccessful: "成功上传 {count} 个文件",
    retrySuccessful: "重试上传成功",
    allSlugConflicts: "所有文件的链接后缀都已被占用，请更换其他后缀",
    allPermissionErrors: "没有权限使用此存储配置",
    allUploadsFailed: "所有文件上传失败",
    someSlugConflicts: "{count} 个文件的链接后缀已被占用",
    someUploadsFailed: "{count} 个文件上传失败",
    singleFileCancelMessage: "文件上传已取消",
    insufficientStorageDetailed: "存储空间不足：文件大小({fileSize})超过剩余空间({remainingSpace})，总容量限制为{totalCapacity}",
    linkCopied: "链接已复制到剪贴板",
    directLinkCopied: "直链已复制到剪贴板",
    copyFailed: "复制失败，请手动复制",
    qrCodeDownloaded: "二维码已下载",
    messages: {
      noS3Config: "请选择存储配置",
      noFilesSelected: "请选择要上传的文件",
      fileTooLarge: "文件大小超过限制",
      uploadFailed: "上传失败",
      uploadCancelled: "上传已取消",
      deleteFailed: "删除失败",
      getFileDetailFailed: "获取文件详情失败",
      cannotGetDirectLink: "无法获取直链，请稍后重试",
      invalidUrl: "请输入有效的URL地址",
      urlAnalysisFailed: "URL分析失败",
      negativeMaxViews: "最大查看次数不能为负数",
      getPresignedUrlFailed: "获取预签名URL失败",
      slugInvalid: "自定义链接只能包含字母、数字、连字符和下划线",
      slugTooLong: "自定义链接不能超过50个字符",
      slugReserved: "此链接为系统保留，请使用其他链接",
      slugConflict: "链接后缀已被占用，请更换其他后缀",
      permissionError: "没有权限使用此存储配置",
      initMultipartUploadFailed: "初始化分片上传失败",
      networkError: "网络错误，请检查网络连接",
      serverError: "服务器错误，请稍后重试",
      unknownError: "未知错误",
    },
    confirmDelete: "确认删除",
    confirmDeleteMessage: "确定要删除这个文件吗？此操作不可撤销。",
    confirm: "确认",
    qrCodeTitle: "文件二维码",
    qrCodeGenerating: "生成中...",
    qrCodeScanToAccess: "扫描二维码访问文件",
    uploadProgress: "上传进度",
    uploadSpeed: "上传速度",
    uploadStage: {
      starting: "准备上传...",
      initializing: "初始化...",
      uploading: "上传中...",
      processing: "处理中...",
      completing: "完成中...",
      completed: "上传完成",
    },
  },
};
