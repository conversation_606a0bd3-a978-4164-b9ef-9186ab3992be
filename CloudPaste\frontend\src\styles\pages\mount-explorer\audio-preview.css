

.audio-preview-container {
  width: 100%;
}

.audio-preview {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .audio-preview {
    padding: 0.75rem !important;
    min-height: 100px;
  }
}
