export default {
  "keyManagement": {
    "title": "API密钥管理",
    "refresh": "刷新",
    "bulkDelete": "批量删除",
    "delete": "删除",
    "create": "创建新密钥",
    "createShort": "创建",
    "lastRefreshed": "最后刷新",
    "table": {
      "select": "选择",
      "name": "名称",
      "key": "密钥",
      "permissions": "权限",
      "basicPath": "基础路径",
      "expires": "过期时间",
      "lastUsed": "最后使用",
      "actions": "操作",
      "noData": "暂无API密钥",
      "loading": "加载中..."
    },
    "keyName": "名称",
    "key": "密钥",
    "permissions": {
      "text": "文本",
      "file": "文件",
      "mount": "挂载",
      "readOnly": "只读",
      "none": "无"
    },
    "permissionsColumn": "权限",
    "basicPath": "基础路径",
    "createdAt": "创建时间",
    "expiresAt": "过期时间",
    "lastUsed": "最后使用",
    "actions": "操作",
    "loading": "加载中...",
    "loadingKeys": "加载密钥中...",
    "neverUsed": "从未使用",
    "noKeysTitle": "暂无API密钥",
    "noKeysDescription": "您还没有创建任何API密钥，点击上方按钮创建第一个密钥",
    "textPermissionFull": "文本",
    "filePermissionFull": "文件",
    "mountPermissionFull": "挂载",
    "noPermission": "无权限",
    "copyKey": "复制",
    "copyKeyFull": "复制完整密钥",
    "edit": "编辑",
    "deleteKey": "删除",
    "neverExpires": "永不过期",
    "createModal": {
      "title": "创建新API密钥",
      "tabs": {
        "basic": "基本信息",
        "path": "路径选择"
      },
      "keyName": "密钥名称",
      "keyNamePlaceholder": "输入密钥名称",
      "keyNameHelp": "为您的API密钥设置一个易于识别的名称",
      "useCustomKey": "使用自定义密钥",
      "customKey": "自定义密钥",
      "customKeyPlaceholder": "输入自定义密钥（可选）",
      "customKeyHelp": "只能包含字母、数字、下划线和短横线",
      "expiration": "过期时间",
      "expirationOptions": {
        "1d": "1天",
        "7d": "7天",
        "30d": "30天",
        "never": "永不过期",
        "custom": "自定义"
      },
      "customExpiration": "自定义过期时间",
      "customExpirationPlaceholder": "请输入天数",
      "permissions": {
        "text": "文本权限",
        "file": "文件权限",
        "mount": "挂载权限"
      },
      "textPermission": "文本权限",
      "filePermission": "文件权限",
      "mountPermission": "挂载权限",
      "readOnlyMount": "只读挂载",
      "basicPath": "基本路径",
      "basicPathPlaceholder": "/",
      "basicPathHelp": "设置API密钥可访问的基本路径，默认为根路径",
      "selectPath": "选择路径",
      "securityTip": "安全提示",
      "securityMessage": "请妥善保管您的API密钥，不要在公共场所或不安全的环境中使用。",
      "pathSelector": {
        "title": "选择基础路径",
        "rootDirectory": "根目录",
        "selectDirectory": "选择目录",
        "currentPath": "当前选择",
        "confirm": "确认路径",
        "cancel": "取消",
        "loading": "加载中...",
        "loadError": "加载失败",
        "noDirectories": "此目录下没有子目录"
      },
      "create": "创建",
      "creating": "创建中...",
      "processing": "创建中...",
      "cancel": "取消",
      "errors": {
        "nameRequired": "密钥名称不能为空",
        "customKeyRequired": "自定义密钥不能为空",
        "customKeyFormat": "自定义密钥格式不正确，只能包含字母、数字、下划线和短横线",
        "expirationRequired": "自定义过期时间不能为空",
        "invalidExpiration": "无效的过期时间",
        "createFailed": "创建密钥失败"
      }
    },
    "editModal": {
      "title": "编辑API密钥",
      "tabs": {
        "basic": "基本信息",
        "path": "路径选择"
      },
      "keyName": "密钥名称",
      "keyNamePlaceholder": "请输入密钥名称",
      "keyNameHelp": "为您的API密钥设置一个易于识别的名称",
      "expiration": "过期时间",
      "expirationOptions": {
        "1d": "1天",
        "7d": "7天",
        "30d": "30天",
        "never": "永不过期",
        "custom": "自定义"
      },
      "customExpiration": "自定义过期时间",
      "customExpirationPlaceholder": "请输入天数",
      "permissions": {
        "text": "文本权限",
        "file": "文件权限",
        "mount": "挂载权限"
      },
      "textPermission": "文本权限",
      "filePermission": "文件权限",
      "mountPermission": "挂载权限",
      "basicPath": "基本路径",
      "basicPathPlaceholder": "/",
      "basicPathHelp": "设置API密钥可访问的基本路径，默认为根路径",
      "selectPath": "选择路径",
      "securityTip": "安全提示",
      "securityMessage": "请妥善保管您的API密钥，不要在公共场所或不安全的环境中使用。",
      "pathSelector": {
        "title": "选择基础路径",
        "rootDirectory": "根目录",
        "selectDirectory": "选择目录",
        "currentPath": "当前选择",
        "confirm": "确认路径",
        "cancel": "取消",
        "loading": "加载中...",
        "loadError": "加载失败",
        "noDirectories": "此目录下没有子目录"
      },
      "update": "更新",
      "updating": "更新中...",
      "processing": "更新中...",
      "cancel": "取消",
      "errors": {
        "nameRequired": "密钥名称不能为空",
        "expirationRequired": "自定义过期时间不能为空",
        "invalidExpiration": "无效的过期时间",
        "updateFailed": "更新密钥失败"
      }
    },
    "success": {
      "created": "API密钥创建成功",
      "createdAndCopied": "密钥已创建并复制到剪贴板",
      "updated": "API密钥更新成功",
      "deleted": "API密钥删除成功",
      "bulkDeleted": "批量删除成功，共删除 {count} 个密钥",
      "copied": "密钥已复制到剪贴板"
    },
    "error": {
      "cannotLoadList": "无法加载密钥列表",
      "loadFailed": "加载API密钥失败",
      "copyFailed": "复制到剪贴板失败",
      "deleteFailed": "删除密钥失败",
      "bulkDeleteFailed": "批量删除失败",
      "noKeysSelected": "请选择要删除的密钥"
    },
    "confirmDelete": "确定要删除密钥 \"{name}\" 吗？此操作不可撤销。",
    "confirmBulkDelete": "确定要删除选中的 {count} 个密钥吗？此操作不可撤销。",
    "selectKeysFirst": "请先选择要删除的密钥",
    "bulkDeleteConfirm": "确定要删除选中的 {count} 个密钥吗？此操作不可撤销。"
  }
};