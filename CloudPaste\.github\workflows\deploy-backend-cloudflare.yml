name: Deploy Backend to <PERSON>flare Workers

on:
  push:
    branches: [main, master]
    paths:
      - "backend/**"
  workflow_dispatch:
  repository_dispatch:
    types: [deploy-button]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: "./backend/package-lock.json"

      - name: Install dependencies
        run: |
          echo "安装依赖（不包含可选依赖）..."
          if npm ci --no-optional; then
            echo "✅ npm ci 成功"
          else
            echo "⚠️ npm ci 失败，将使用npm install更新lock文件"
            npm install --no-optional
          fi

      - name: Disable wrangler telemetry
        run: npx wrangler telemetry disable

      - name: Check if deploy button trigger
        id: check-deploy-button
        run: |
          if [[ "${{ github.event_name }}" == "repository_dispatch" && "${{ github.event.action }}" == "deploy-button" ]]; then
            echo "is_deploy_button=true" >> $GITHUB_OUTPUT
          else
            echo "is_deploy_button=false" >> $GITHUB_OUTPUT
          fi

      - name: Create D1 Database
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: |
          echo "检查并创建D1数据库..."
          DATABASE_LIST=$(npx wrangler d1 list --json 2>/dev/null || echo "[]")
          EXISTING_DB=$(echo "$DATABASE_LIST" | jq -r '.[] | select(.name=="cloudpaste-db") | .uuid')

          if [ -n "$EXISTING_DB" ]; then
            echo "✅ 找到D1数据库: $EXISTING_DB"
            DATABASE_ID=$EXISTING_DB
          else
            CREATE_OUTPUT=$(npx wrangler d1 create cloudpaste-db 2>&1)

            if echo "$CREATE_OUTPUT" | grep -q "Successfully created DB"; then
              DATABASE_ID=$(echo "$CREATE_OUTPUT" | grep -oP "database_id = \"\K[^\"]+")
              if [ -z "$DATABASE_ID" ]; then
                DATABASE_ID=$(echo "$CREATE_OUTPUT" | grep -oP "([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})")
              fi
            else
              LIST_OUTPUT=$(npx wrangler d1 list --json 2>/dev/null || echo "[]")
              DATABASE_ID=$(echo "$LIST_OUTPUT" | jq -r '.[] | select(.name=="cloudpaste-db") | .uuid')

              if [ -z "$DATABASE_ID" ]; then
                echo "❌ 无法创建或找到D1数据库"
                exit 1
              fi
            fi
          fi

          sed -i -E "s/(database_id = \")[^\"]+(\"\s*$)/\1$DATABASE_ID\2/" wrangler.toml
          echo "database_id=$DATABASE_ID" >> $GITHUB_ENV

      - name: Initialize D1 Database with schema
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: |
          npx wrangler d1 execute cloudpaste-db --file=./schema.sql || echo "表可能已存在，继续部署"

      - name: Set ENCRYPTION_SECRET environment variable
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: |
          echo "检查ENCRYPTION_SECRET配置情况..."

          # 1. 检查wrangler.toml中是否有硬编码的ENCRYPTION_SECRET
          if grep -q "ENCRYPTION_SECRET =" wrangler.toml; then
            echo "⚠️ 检测到wrangler.toml中存在硬编码的ENCRYPTION_SECRET"
            echo "为确保GitHub Actions的密钥设置生效，将从wrangler.toml中移除硬编码密钥"

            # 临时备份wrangler.toml
            cp wrangler.toml wrangler.toml.bak

            # 移除硬编码的ENCRYPTION_SECRET行
            sed -i '/ENCRYPTION_SECRET =/d' wrangler.toml

            echo "✅ 已从wrangler.toml中移除硬编码的ENCRYPTION_SECRET"
          else
            echo "✅ wrangler.toml中未检测到硬编码的ENCRYPTION_SECRET"
          fi

          # 2. 检查GitHub中是否已配置ENCRYPTION_SECRET
          if [[ -n "${{ secrets.ENCRYPTION_SECRET }}" ]]; then
            echo "✅ GitHub中已配置ENCRYPTION_SECRET"
            GITHUB_HAS_SECRET=true
          else
            echo "⚠️ GitHub中未配置ENCRYPTION_SECRET"
            GITHUB_HAS_SECRET=false
          fi

          # 3. 检查Worker中是否已存在ENCRYPTION_SECRET (作为secret变量)
          set +e
          SECRET_LIST_OUTPUT=$(npx wrangler secret list 2>&1)
          set -e

          if echo "$SECRET_LIST_OUTPUT" | grep -q "ENCRYPTION_SECRET"; then
            echo "✅ Worker中已存在ENCRYPTION_SECRET(作为secret变量)"
            WORKER_HAS_SECRET=true
          else
            echo "⚠️ Worker中未检测到ENCRYPTION_SECRET(作为secret变量)"
            WORKER_HAS_SECRET=false
          fi

          # 如果Worker中已有密钥，则跳过创建
          if [[ "$WORKER_HAS_SECRET" == "true" ]]; then
            echo "✅ Worker中已存在ENCRYPTION_SECRET(作为secret变量)，跳过创建步骤"
          else
            # 确定要使用的密钥值
            if [[ "$GITHUB_HAS_SECRET" == "true" ]]; then
              echo "使用GitHub中配置的ENCRYPTION_SECRET值"
              ENCRYPTION_VALUE="${{ secrets.ENCRYPTION_SECRET }}"
            else
              echo "生成随机ENCRYPTION_SECRET值"
              ENCRYPTION_VALUE=$(openssl rand -base64 32)
            fi

            # 设置密钥到Worker
            set +e
            echo "正在设置ENCRYPTION_SECRET..."
            SECRET_PUT_OUTPUT=$(echo "$ENCRYPTION_VALUE" | npx wrangler secret put ENCRYPTION_SECRET 2>&1)
            SECRET_RESULT=$?
            set -e

            echo "Secret put 输出:"
            echo "$SECRET_PUT_OUTPUT" | grep -v "Please update to the latest version"

            if [ $SECRET_RESULT -ne 0 ]; then
              # 如果错误是由于密钥已存在导致的，视为成功
              if echo "$SECRET_PUT_OUTPUT" | grep -q -E "(already in use|already exists|conflict)"; then
                echo "⚠️ 密钥已存在于Worker中但未被列表命令检测到，继续执行"
              else
                # 最后再检查一次是否因为密钥已存在但未被正确检测
                set +e
                FINAL_CHECK=$(npx wrangler secret list 2>&1)
                set -e

                if echo "$FINAL_CHECK" | grep -q "ENCRYPTION_SECRET"; then
                  echo "虽然设置密钥失败，但密钥似乎已存在于Worker中，继续执行"
                else
                  echo "❌ 设置密钥失败，且密钥确实不存在，退出部署"
                  echo "详细错误信息: $SECRET_PUT_OUTPUT"
                  exit 1
                fi
              fi
            else
              echo "✅ ENCRYPTION_SECRET 已成功创建(作为secret变量)"
            fi
          fi

      - name: Deploy to Cloudflare Workers
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: |
          npx wrangler deploy 2>&1 | sed -E 's/https:\/\/[a-zA-Z0-9.-]*\.(workers|pages)\.dev/https:\/\/[REDACTED].\1.dev/g'

      - name: Display Success Information
        if: steps.check-deploy-button.outputs.is_deploy_button == 'true' && success()
        run: |
          echo "===================================================="
          echo "🎉 CloudPaste 后端已成功部署！"
          echo "===================================================="
          echo "后续步骤："
          echo "1. 部署前端应用"
          echo "2. 访问您的Worker URL进行初始化"
          echo "3. 登录默认账户 (admin/admin123) 并立即修改密码"
          echo "4. 配置S3兼容存储"
          echo "===================================================="

      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ 部署成功！"
          else
            echo "❌ 部署失败！"
          fi
