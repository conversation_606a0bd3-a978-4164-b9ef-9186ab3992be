!function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=38)}([function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var a,s=r(2),l=r(33);!function(t){var e=new Map([["autoOP",!0],["fnOP",!0],["movesupsub",!0],["subsupOK",!0],["texprimestyle",!0],["useHeight",!0],["variantForm",!0],["withDelims",!0],["open",!0],["close",!0]]);function r(t,r){var i,o;try{for(var a=n(Object.keys(r)),s=a.next();!s.done;s=a.next()){var l=s.value,u=r[l];"texClass"===l?(t.texClass=u,t.setProperty(l,u)):"movablelimits"===l?(t.setProperty("movablelimits",u),(t.isKind("mo")||t.isKind("mstyle"))&&t.attributes.set("movablelimits",u)):"inferred"===l||(e.has(l)?t.setProperty(l,u):t.attributes.set(l,u))}}catch(t){i={error:t}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(i)throw i.error}}}function i(t,e,r){t.childNodes[e]=r,r&&(r.parent=t)}function a(t,e){return t.isKind(e)}t.createEntity=function(t){return String.fromCodePoint(parseInt(t,16))},t.getChildren=function(t){return t.childNodes},t.getText=function(t){return t.getText()},t.appendChildren=function(t,e){var r,i;try{for(var o=n(e),a=o.next();!a.done;a=o.next()){var s=a.value;t.appendChild(s)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(i=o.return)&&i.call(o)}finally{if(r)throw r.error}}},t.setAttribute=function(t,e,r){t.attributes.set(e,r)},t.setProperty=function(t,e,r){t.setProperty(e,r)},t.setProperties=r,t.getProperty=function(t,e){return t.getProperty(e)},t.getAttribute=function(t,e){return t.attributes.get(e)},t.removeProperties=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.removeProperty.apply(t,o(e))},t.getChildAt=function(t,e){return t.childNodes[e]},t.setChild=i,t.copyChildren=function(t,e){for(var r=t.childNodes,n=0;n<r.length;n++)i(e,n,r[n])},t.copyAttributes=function(t,e){e.attributes=t.attributes,r(e,t.getAllProperties())},t.isType=a,t.isEmbellished=function(t){return t.isEmbellished},t.getTexClass=function(t){return t.texClass},t.getCoreMO=function(t){return t.coreMO()},t.isNode=function(t){return t instanceof s.AbstractMmlNode||t instanceof s.AbstractMmlEmptyNode},t.isInferred=function(t){return t.isInferred},t.getForm=function(t){var e,r;if(!a(t,"mo"))return null;var i=t,o=i.getForms();try{for(var s=n(o),u=s.next();!u.done;u=s.next()){var c=u.value,p=l.MmlMo.OPTABLE[c][i.getText()];if(p)return p}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return null}}(a||(a={})),e.default=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(e,r){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];this.id=e,this.message=t.processString(r,n)}return t.processString=function(e,r){for(var n=e.split(t.pattern),i=1,o=n.length;i<o;i+=2){var a=n[i].charAt(0);if(a>="0"&&a<="9")n[i]=r[parseInt(n[i],10)-1],"number"==typeof n[i]&&(n[i]=n[i].toString());else if("{"===a){if((a=n[i].substr(1))>="0"&&a<="9")n[i]=r[parseInt(n[i].substr(1,n[i].length-2),10)-1],"number"==typeof n[i]&&(n[i]=n[i].toString());else n[i].match(/^\{([a-z]+):%(\d+)\|(.*)\}$/)&&(n[i]="%"+n[i])}null==n[i]&&(n[i]="???")}return n.join("")},t.pattern=/%(\d+|\{\d+\}|\{[a-z]+:\%\d+(?:\|(?:%\{\d+\}|%.|[^\}])*)+\}|.)/g,t}();e.default=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.options=MathJax._.util.Entities.options,e.entities=MathJax._.util.Entities.entities,e.add=MathJax._.util.Entities.add,e.remove=MathJax._.util.Entities.remove,e.translate=MathJax._.util.Entities.translate,e.numeric=MathJax._.util.Entities.numeric},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var a=r(5),s=r(17),l=r(1),u=r(2),c=function(){function t(t,e,r){var i,o;this._string=t,this.configuration=r,this.macroCount=0,this.i=0,this.currentCS="";var a,l=e.hasOwnProperty("isInner"),u=e.isInner;if(delete e.isInner,e){a={};try{for(var c=n(Object.keys(e)),p=c.next();!p.done;p=c.next()){var f=p.value;a[f]=e[f]}}catch(t){i={error:t}}finally{try{p&&!p.done&&(o=c.return)&&o.call(c)}finally{if(i)throw i.error}}}this.configuration.pushParser(this),this.stack=new s.default(this.itemFactory,a,!l||u),this.Parse(),this.Push(this.itemFactory.create("stop"))}return Object.defineProperty(t.prototype,"options",{get:function(){return this.configuration.options},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"itemFactory",{get:function(){return this.configuration.itemFactory},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tags",{get:function(){return this.configuration.tags},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"string",{get:function(){return this._string},set:function(t){this._string=t},enumerable:!1,configurable:!0}),t.prototype.parse=function(t,e){return this.configuration.handlers.get(t).parse(e)},t.prototype.lookup=function(t,e){return this.configuration.handlers.get(t).lookup(e)},t.prototype.contains=function(t,e){return this.configuration.handlers.get(t).contains(e)},t.prototype.toString=function(){var t,e,r="";try{for(var i=n(Array.from(this.configuration.handlers.keys())),o=i.next();!o.done;o=i.next()){var a=o.value;r+=a+": "+this.configuration.handlers.get(a)+"\n"}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return r},t.prototype.Parse=function(){for(var t;this.i<this.string.length;)t=this.getCodePoint(),this.i+=t.length,this.parse("character",[this,t])},t.prototype.Push=function(t){t instanceof u.AbstractMmlNode&&t.isInferred?this.PushAll(t.childNodes):this.stack.Push(t)},t.prototype.PushAll=function(t){var e,r;try{for(var i=n(t),o=i.next();!o.done;o=i.next()){var a=o.value;this.stack.Push(a)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},t.prototype.mml=function(){if(!this.stack.Top().isKind("mml"))return null;var t=this.stack.Top().First;return this.configuration.popParser(),t},t.prototype.convertDelimiter=function(t){var e=this.lookup("delimiter",t);return e?e.char:null},t.prototype.getCodePoint=function(){var t=this.string.codePointAt(this.i);return void 0===t?"":String.fromCodePoint(t)},t.prototype.nextIsSpace=function(){return!!this.string.charAt(this.i).match(/\s/)},t.prototype.GetNext=function(){for(;this.nextIsSpace();)this.i++;return this.getCodePoint()},t.prototype.GetCS=function(){var t=this.string.slice(this.i).match(/^(([a-z]+) ?|[\uD800-\uDBFF].|.)/i);return t?(this.i+=t[0].length,t[2]||t[1]):(this.i++," ")},t.prototype.GetArgument=function(t,e){switch(this.GetNext()){case"":if(!e)throw new l.default("MissingArgFor","Missing argument for %1",this.currentCS);return null;case"}":if(!e)throw new l.default("ExtraCloseMissingOpen","Extra close brace or missing open brace");return null;case"\\":return this.i++,"\\"+this.GetCS();case"{":for(var r=++this.i,n=1;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"\\":this.i++;break;case"{":n++;break;case"}":if(0==--n)return this.string.slice(r,this.i-1)}throw new l.default("MissingCloseBrace","Missing close brace")}var i=this.getCodePoint();return this.i+=i.length,i},t.prototype.GetBrackets=function(t,e){if("["!==this.GetNext())return e;for(var r=++this.i,n=0;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"{":n++;break;case"\\":this.i++;break;case"}":if(n--<=0)throw new l.default("ExtraCloseLooking","Extra close brace while looking for %1","']'");break;case"]":if(0===n)return this.string.slice(r,this.i-1)}throw new l.default("MissingCloseBracket","Could not find closing ']' for argument to %1",this.currentCS)},t.prototype.GetDelimiter=function(t,e){var r=this.GetNext();if(this.i+=r.length,this.i<=this.string.length&&("\\"===r?r+=this.GetCS():"{"===r&&e&&(this.i--,r=this.GetArgument(t)),this.contains("delimiter",r)))return this.convertDelimiter(r);throw new l.default("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)},t.prototype.GetDimen=function(t){if("{"===this.GetNext()){var e=this.GetArgument(t),r=i(a.default.matchDimen(e),2),n=r[0],o=r[1];if(n)return n+o}else{e=this.string.slice(this.i);var s=i(a.default.matchDimen(e,!0),3),u=(n=s[0],o=s[1],s[2]);if(n)return this.i+=u,n+o}throw new l.default("MissingDimOrUnits","Missing dimension or its units for %1",this.currentCS)},t.prototype.GetUpTo=function(t,e){for(;this.nextIsSpace();)this.i++;for(var r=this.i,n=0;this.i<this.string.length;){var i=this.i,o=this.GetNext();switch(this.i+=o.length,o){case"\\":o+=this.GetCS();break;case"{":n++;break;case"}":if(0===n)throw new l.default("ExtraCloseLooking","Extra close brace while looking for %1",e);n--}if(0===n&&o===e)return this.string.slice(r,i)}throw new l.default("TokenNotFoundForCommand","Could not find %1 for %2",e,this.currentCS)},t.prototype.ParseArg=function(e){return new t(this.GetArgument(e),this.stack.env,this.configuration).mml()},t.prototype.ParseUpTo=function(e,r){return new t(this.GetUpTo(e,r),this.stack.env,this.configuration).mml()},t.prototype.GetDelimiterArg=function(t){var e=a.default.trimSpaces(this.GetArgument(t));if(""===e)return null;if(this.contains("delimiter",e))return e;throw new l.default("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)},t.prototype.GetStar=function(){var t="*"===this.GetNext();return t&&this.i++,t},t.prototype.create=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.configuration.nodeFactory).create.apply(e,o([t],r))},t}();e.default=c},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var o,a=r(2),s=r(0),l=r(4),u=r(1),c=r(3);r(25),function(t){var e={em:function(t){return t},ex:function(t){return.43*t},pt:function(t){return t/10},pc:function(t){return 1.2*t},px:function(t){return 7.2*t/72},in:function(t){return 7.2*t},cm:function(t){return 7.2*t/2.54},mm:function(t){return 7.2*t/25.4},mu:function(t){return t/18}},r="([-+]?([.,]\\d+|\\d+([.,]\\d*)?))",o="(pt|em|ex|mu|px|mm|cm|in|pc)",p=RegExp("^\\s*"+r+"\\s*"+o+"\\s*$"),f=RegExp("^\\s*"+r+"\\s*"+o+" ?");function h(t,r){void 0===r&&(r=!1);var i=t.match(r?f:p);return i?function(t){var r=n(t,3),i=r[0],o=r[1],a=r[2];if("mu"!==o)return[i,o,a];return[d(e[o](parseFloat(i||"1"))).slice(0,-2),"em",a]}([i[1].replace(/,/,"."),i[4],i[0].length]):[null,null,0]}function d(t){return Math.abs(t)<6e-4?"0em":t.toFixed(3).replace(/\.?0+$/,"")+"em"}function m(t,e,r){"{"!==e&&"}"!==e||(e="\\"+e);var n="{\\bigg"+r+" "+e+"}",i="{\\big"+r+" "+e+"}";return new l.default("\\mathchoice"+n+i+i+i,{},t).mml()}function g(t,e,r){e=e.replace(/^\s+/,c.entities.nbsp).replace(/\s+$/,c.entities.nbsp);var n=t.create("text",e);return t.create("node","mtext",[],r,n)}function y(t,e,r){if(r.match(/^[a-z]/i)&&e.match(/(^|[^\\])(\\\\)*\\[a-z]+$/i)&&(e+=" "),e.length+r.length>t.configuration.options.maxBuffer)throw new u.default("MaxBufferSize","MathJax internal buffer size exceeded; is there a recursive macro call?");return e+r}function v(t,e){for(;e>0;)t=t.trim().slice(1,-1),e--;return t.trim()}function b(t,e){for(var r=t.length,n=0,i="",o=0,a=0,s=!0,l=!1;o<r;){var c=t[o++];switch(c){case" ":break;case"{":s?a++:(l=!1,a>n&&(a=n)),n++;break;case"}":n&&n--,(s||l)&&(a--,l=!0),s=!1;break;default:if(!n&&-1!==e.indexOf(c))return[l?"true":v(i,a),c,t.slice(o)];s=!1,l=!1}i+=c}if(n)throw new u.default("ExtraOpenMissingClose","Extra open brace or missing close brace");return[l?"true":v(i,a),"",t.slice(o)]}t.matchDimen=h,t.dimen2em=function(t){var r=n(h(t),2),i=r[0],o=r[1],a=parseFloat(i||"1"),s=e[o];return s?s(a):0},t.Em=d,t.fenced=function(t,e,r,n,i){void 0===i&&(i="");var o,u=t.nodeFactory,c=u.create("node","mrow",[],{open:e,close:n,texClass:a.TEXCLASS.INNER});if(i)o=new l.default("\\"+i+"l"+e,t.parser.stack.env,t).mml();else{var p=u.create("text",e);o=u.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:a.TEXCLASS.OPEN},p)}if(s.default.appendChildren(c,[o]),s.default.isType(r,"mrow")&&s.default.isInferred(r)?s.default.appendChildren(c,s.default.getChildren(r)):s.default.appendChildren(c,[r]),i)o=new l.default("\\"+i+"r"+n,t.parser.stack.env,t).mml();else{var f=u.create("text",n);o=u.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:a.TEXCLASS.CLOSE},f)}return s.default.appendChildren(c,[o]),c},t.fixedFence=function(t,e,r,n){var i=t.nodeFactory.create("node","mrow",[],{open:e,close:n,texClass:a.TEXCLASS.ORD});return e&&s.default.appendChildren(i,[m(t,e,"l")]),s.default.isType(r,"mrow")?s.default.appendChildren(i,s.default.getChildren(r)):s.default.appendChildren(i,[r]),n&&s.default.appendChildren(i,[m(t,n,"r")]),i},t.mathPalette=m,t.fixInitialMO=function(t,e){for(var r=0,n=e.length;r<n;r++){var i=e[r];if(i&&!s.default.isType(i,"mspace")&&(!s.default.isType(i,"TeXAtom")||s.default.getChildren(i)[0]&&s.default.getChildren(s.default.getChildren(i)[0]).length)){if(s.default.isEmbellished(i)||s.default.isType(i,"TeXAtom")&&s.default.getTexClass(i)===a.TEXCLASS.REL){var o=t.nodeFactory.create("node","mi");e.unshift(o)}break}}},t.internalMath=function(t,e,r,n){if(t.configuration.options.internalMath)return t.configuration.options.internalMath(t,e,r,n);var i,o,a=n||t.stack.env.font,s=a?{mathvariant:a}:{},c=[],p=0,f=0,h="",d=0;if(e.match(/\\?[${}\\]|\\\(|\\(eq)?ref\s*\{/)){for(;p<e.length;)if("$"===(i=e.charAt(p++)))"$"===h&&0===d?(o=t.create("node","TeXAtom",[new l.default(e.slice(f,p-1),{},t.configuration).mml()]),c.push(o),h="",f=p):""===h&&(f<p-1&&c.push(g(t,e.slice(f,p-1),s)),h="$",f=p);else if("{"===i&&""!==h)d++;else if("}"===i)if("}"===h&&0===d){var m=new l.default(e.slice(f,p),{},t.configuration).mml();o=t.create("node","TeXAtom",[m],s),c.push(o),h="",f=p}else""!==h&&d&&d--;else if("\\"===i)if(""===h&&e.substr(p).match(/^(eq)?ref\s*\{/)){var y=RegExp["$&"].length;f<p-1&&c.push(g(t,e.slice(f,p-1),s)),h="}",f=p-1,p+=y}else"("===(i=e.charAt(p++))&&""===h?(f<p-2&&c.push(g(t,e.slice(f,p-2),s)),h=")",f=p):")"===i&&")"===h&&0===d?(o=t.create("node","TeXAtom",[new l.default(e.slice(f,p-2),{},t.configuration).mml()]),c.push(o),h="",f=p):i.match(/[${}\\]/)&&""===h&&(p--,e=e.substr(0,p-1)+e.substr(p));if(""!==h)throw new u.default("MathNotTerminated","Math not terminated in text box")}return f<e.length&&c.push(g(t,e.slice(f),s)),null!=r?c=[t.create("node","mstyle",c,{displaystyle:!1,scriptlevel:r})]:c.length>1&&(c=[t.create("node","mrow",c)]),c},t.internalText=g,t.trimSpaces=function(t){if("string"!=typeof t)return t;var e=t.trim();return e.match(/\\$/)&&t.match(/ $/)&&(e+=" "),e},t.setArrayAlign=function(e,r){return"t"===(r=t.trimSpaces(r||""))?e.arraydef.align="baseline 1":"b"===r?e.arraydef.align="baseline -1":"c"===r?e.arraydef.align="center":r&&(e.arraydef.align=r),e},t.substituteArgs=function(t,e,r){for(var n="",i="",o=0;o<r.length;){var a=r.charAt(o++);if("\\"===a)n+=a+r.charAt(o++);else if("#"===a)if("#"===(a=r.charAt(o++)))n+=a;else{if(!a.match(/[1-9]/)||parseInt(a,10)>e.length)throw new u.default("IllegalMacroParam","Illegal macro parameter reference");i=y(t,y(t,i,n),e[parseInt(a,10)-1]),n=""}else n+=a}return y(t,i,n)},t.addArgs=y,t.checkEqnEnv=function(t){if(t.stack.global.eqnenv)throw new u.default("ErroneousNestingEq","Erroneous nesting of equation structures");t.stack.global.eqnenv=!0},t.MmlFilterAttribute=function(t,e,r){return r},t.getFontDef=function(t){var e=t.stack.env.font;return e?{mathvariant:e}:{}},t.keyvalOptions=function(t,e,r){var o,a;void 0===e&&(e=null),void 0===r&&(r=!1);var s=function(t){var e,r,i,o,a,s={},l=t;for(;l;)o=(e=n(b(l,["=",","]),3))[0],i=e[1],l=e[2],"="===i?(a=(r=n(b(l,[","]),3))[0],i=r[1],l=r[2],a="false"===a||"true"===a?JSON.parse(a):a,s[o]=a):o&&(s[o]=!0);return s}(t);if(e)try{for(var l=i(Object.keys(s)),c=l.next();!c.done;c=l.next()){var p=c.value;if(!e.hasOwnProperty(p)){if(r)throw new u.default("InvalidOption","Invalid optional argument: %1",p);delete s[p]}}}catch(t){o={error:t}}finally{try{c&&!c.done&&(a=l.return)&&a.call(l)}finally{if(o)throw o.error}}return s}}(o||(o={})),e.default=o},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.TagsFactory=e.AllTags=e.NoTags=e.AbstractTags=e.TagInfo=e.Label=void 0;var a=r(4),s=function(t,e){void 0===t&&(t="???"),void 0===e&&(e=""),this.tag=t,this.id=e};e.Label=s;var l=function(t,e,r,n,i,o,a,s){void 0===t&&(t=""),void 0===e&&(e=!1),void 0===r&&(r=!1),void 0===n&&(n=null),void 0===i&&(i=""),void 0===o&&(o=""),void 0===a&&(a=!1),void 0===s&&(s=""),this.env=t,this.taggable=e,this.defaultTags=r,this.tag=n,this.tagId=i,this.tagFormat=o,this.noTag=a,this.labelId=s};e.TagInfo=l;var u=function(){function t(){this.counter=0,this.allCounter=0,this.configuration=null,this.ids={},this.allIds={},this.labels={},this.allLabels={},this.redo=!1,this.refUpdate=!1,this.currentTag=new l,this.history=[],this.stack=[],this.enTag=function(t,e){var r=this.configuration.nodeFactory,n=r.create("node","mtd",[t]),i=r.create("node","mlabeledtr",[e,n]);return r.create("node","mtable",[i],{side:this.configuration.options.tagSide,minlabelspacing:this.configuration.options.tagIndent,displaystyle:!0})}}return t.prototype.start=function(t,e,r){this.currentTag&&this.stack.push(this.currentTag),this.currentTag=new l(t,e,r)},Object.defineProperty(t.prototype,"env",{get:function(){return this.currentTag.env},enumerable:!1,configurable:!0}),t.prototype.end=function(){this.history.push(this.currentTag),this.currentTag=this.stack.pop()},t.prototype.tag=function(t,e){this.currentTag.tag=t,this.currentTag.tagFormat=e?t:this.formatTag(t),this.currentTag.noTag=!1},t.prototype.notag=function(){this.tag("",!0),this.currentTag.noTag=!0},Object.defineProperty(t.prototype,"noTag",{get:function(){return this.currentTag.noTag},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return this.currentTag.labelId},set:function(t){this.currentTag.labelId=t},enumerable:!1,configurable:!0}),t.prototype.formatUrl=function(t,e){return e+"#"+encodeURIComponent(t)},t.prototype.formatTag=function(t){return"("+t+")"},t.prototype.formatId=function(t){return"mjx-eqn-"+t.replace(/\s/g,"_")},t.prototype.formatNumber=function(t){return t.toString()},t.prototype.autoTag=function(){null==this.currentTag.tag&&(this.counter++,this.tag(this.formatNumber(this.counter),!1))},t.prototype.clearTag=function(){this.label="",this.tag(null,!0),this.currentTag.tagId=""},t.prototype.getTag=function(t){if(void 0===t&&(t=!1),t)return this.autoTag(),this.makeTag();var e=this.currentTag;return e.taggable&&!e.noTag&&(e.defaultTags&&this.autoTag(),e.tag)?this.makeTag():null},t.prototype.resetTag=function(){this.history=[],this.redo=!1,this.refUpdate=!1,this.clearTag()},t.prototype.reset=function(t){void 0===t&&(t=0),this.resetTag(),this.counter=this.allCounter=t,this.allLabels={},this.allIds={}},t.prototype.startEquation=function(t){this.labels={},this.ids={},this.counter=this.allCounter,this.redo=!1;var e=t.inputData.recompile;e&&(this.refUpdate=!0,this.counter=e.counter)},t.prototype.finishEquation=function(t){this.redo&&(t.inputData.recompile={state:t.state(),counter:this.allCounter}),this.refUpdate||(this.allCounter=this.counter),Object.assign(this.allIds,this.ids),Object.assign(this.allLabels,this.labels)},t.prototype.finalize=function(t,e){if(!e.display||this.currentTag.env||null==this.currentTag.tag)return t;var r=this.makeTag();return this.enTag(t,r)},t.prototype.makeId=function(){this.currentTag.tagId=this.formatId(this.configuration.options.useLabelIds&&this.label||this.currentTag.tag)},t.prototype.makeTag=function(){this.makeId(),this.label&&(this.labels[this.label]=new s(this.currentTag.tag,this.currentTag.tagId));var t=new a.default("\\text{"+this.currentTag.tagFormat+"}",{},this.configuration).mml();return this.configuration.nodeFactory.create("node","mtd",[t],{id:this.currentTag.tagId})},t}();e.AbstractTags=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.autoTag=function(){},e.prototype.getTag=function(){return this.currentTag.tag?t.prototype.getTag.call(this):null},e}(u);e.NoTags=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.finalize=function(t,e){if(!e.display||this.history.find((function(t){return t.taggable})))return t;var r=this.getTag(!0);return this.enTag(t,r)},e}(u);e.AllTags=p,function(t){var e=new Map([["none",c],["all",p]]),r="none";t.OPTIONS={tags:r,tagSide:"right",tagIndent:"0.8em",multlineWidth:"85%",useLabelIds:!0,ignoreDuplicateLabels:!1},t.add=function(t,r){e.set(t,r)},t.addTags=function(e){var r,n;try{for(var i=o(Object.keys(e)),a=i.next();!a.done;a=i.next()){var s=a.value;t.add(s,e[s])}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}},t.create=function(t){var n=e.get(t)||e.get(r);if(!n)throw Error("Unknown tags class");return new n},t.setDefault=function(t){r=t},t.getDefault=function(){return t.create(r)}}(e.TagsFactory||(e.TagsFactory={}))},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.SubHandlers=e.SubHandler=e.MapHandler=void 0;var o,a=r(26),s=r(27);!function(t){var e=new Map;t.register=function(t){e.set(t.name,t)},t.getMap=function(t){return e.get(t)}}(o=e.MapHandler||(e.MapHandler={}));var l=function(){function t(){this._configuration=new a.PrioritizedList,this._fallback=new s.FunctionList}return t.prototype.add=function(t,e,r){var i,s;void 0===r&&(r=a.PrioritizedList.DEFAULTPRIORITY);try{for(var l=n(t.slice().reverse()),u=l.next();!u.done;u=l.next()){var c=u.value,p=o.getMap(c);if(!p)return void this.warn("Configuration "+c+" not found! Omitted.");this._configuration.add(p,r)}}catch(t){i={error:t}}finally{try{u&&!u.done&&(s=l.return)&&s.call(l)}finally{if(i)throw i.error}}e&&this._fallback.add(e,r)},t.prototype.parse=function(t){var e,r;try{for(var o=n(this._configuration),a=o.next();!a.done;a=o.next()){var s=a.value.item.parse(t);if(s)return s}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}var l=i(t,2),u=l[0],c=l[1];this._fallback.toArray()[0].item(u,c)},t.prototype.lookup=function(t){var e=this.applicable(t);return e?e.lookup(t):null},t.prototype.contains=function(t){return!!this.applicable(t)},t.prototype.toString=function(){var t,e,r=[];try{for(var i=n(this._configuration),o=i.next();!o.done;o=i.next()){var a=o.value.item;r.push(a.name)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return r.join(", ")},t.prototype.applicable=function(t){var e,r;try{for(var i=n(this._configuration),o=i.next();!o.done;o=i.next()){var a=o.value.item;if(a.contains(t))return a}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return null},t.prototype.retrieve=function(t){var e,r;try{for(var i=n(this._configuration),o=i.next();!o.done;o=i.next()){var a=o.value.item;if(a.name===t)return a}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return null},t.prototype.warn=function(t){console.log("TexParser Warning: "+t)},t}();e.SubHandler=l;var u=function(){function t(){this.map=new Map}return t.prototype.add=function(t,e,r){var i,o;void 0===r&&(r=a.PrioritizedList.DEFAULTPRIORITY);try{for(var s=n(Object.keys(t)),u=s.next();!u.done;u=s.next()){var c=u.value,p=this.get(c);p||(p=new l,this.set(c,p)),p.add(t[c],e[c],r)}}catch(t){i={error:t}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(i)throw i.error}}},t.prototype.set=function(t,e){this.map.set(t,e)},t.prototype.get=function(t){return this.map.get(t)},t.prototype.retrieve=function(t){var e,r;try{for(var i=n(this.map.values()),o=i.next();!o.done;o=i.next()){var a=o.value.retrieve(t);if(a)return a}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return null},t.prototype.keys=function(){return this.map.keys()},t}();e.SubHandlers=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TexConstant=void 0,function(t){t.Variant={NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"inital",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALLIGRAPHIC:"-tex-calligraphic",OLDSTYLE:"-tex-oldstyle"},t.Form={PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},t.LineBreak={AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},t.LineBreakStyle={BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},t.IndentAlign={LEFT:"left",CENTER:"center",RIGHT:"right",AUTO:"auto",ID:"id",INDENTALIGN:"indentalign"},t.IndentShift={INDENTSHIFT:"indentshift"},t.LineThickness={THIN:"thin",MEDIUM:"medium",THICK:"thick"},t.Notation={LONGDIV:"longdiv",ACTUARIAL:"actuarial",PHASORANGLE:"phasorangle",RADICAL:"radical",BOX:"box",ROUNDEDBOX:"roundedbox",CIRCLE:"circle",LEFT:"left",RIGHT:"right",TOP:"top",BOTTOM:"bottom",UPDIAGONALSTRIKE:"updiagonalstrike",DOWNDIAGONALSTRIKE:"downdiagonalstrike",VERTICALSTRIKE:"verticalstrike",HORIZONTALSTRIKE:"horizontalstrike",NORTHEASTARROW:"northeastarrow",MADRUWB:"madruwb",UPDIAGONALARROW:"updiagonalarrow"},t.Align={TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},t.Lines={NONE:"none",SOLID:"solid",DASHED:"dashed"},t.Side={LEFT:"left",RIGHT:"right",LEFTOVERLAP:"leftoverlap",RIGHTOVERLAP:"rightoverlap"},t.Width={AUTO:"auto",FIT:"fit"},t.Actiontype={TOGGLE:"toggle",STATUSLINE:"statusline",TOOLTIP:"tooltip",INPUT:"input"},t.Length={VERYVERYTHINMATHSPACE:"veryverythinmathspace",VERYTHINMATHSPACE:"verythinmathspace",THINMATHSPACE:"thinmathspace",MEDIUMMATHSPACE:"mediummathspace",THICKMATHSPACE:"thickmathspace",VERYTHICKMATHSPACE:"verythickmathspace",VERYVERYTHICKMATHSPACE:"veryverythickmathspace",NEGATIVEVERYVERYTHINMATHSPACE:"negativeveryverythinmathspace",NEGATIVEVERYTHINMATHSPACE:"negativeverythinmathspace",NEGATIVETHINMATHSPACE:"negativethinmathspace",NEGATIVEMEDIUMMATHSPACE:"negativemediummathspace",NEGATIVETHICKMATHSPACE:"negativethickmathspace",NEGATIVEVERYTHICKMATHSPACE:"negativeverythickmathspace",NEGATIVEVERYVERYTHICKMATHSPACE:"negativeveryverythickmathspace"},t.Overflow={LINBREAK:"linebreak",SCROLL:"scroll",ELIDE:"elide",TRUNCATE:"truncate",SCALE:"scale"},t.Unit={EM:"em",EX:"ex",PX:"px",IN:"in",CM:"cm",MM:"mm",PT:"pt",PC:"pc"}}(e.TexConstant||(e.TexConstant={}))},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.BaseItem=e.MmlStack=void 0;var l=r(1),u=function(){function t(t){this._nodes=t}return Object.defineProperty(t.prototype,"nodes",{get:function(){return this._nodes},enumerable:!1,configurable:!0}),t.prototype.Push=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._nodes).push.apply(t,a(e))},t.prototype.Pop=function(){return this._nodes.pop()},Object.defineProperty(t.prototype,"First",{get:function(){return this._nodes[this.Size()-1]},set:function(t){this._nodes[this.Size()-1]=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Last",{get:function(){return this._nodes[0]},set:function(t){this._nodes[0]=t},enumerable:!1,configurable:!0}),t.prototype.Peek=function(t){return null==t&&(t=1),this._nodes.slice(this.Size()-t)},t.prototype.Size=function(){return this._nodes.length},t.prototype.Clear=function(){this._nodes=[]},t.prototype.toMml=function(t,e){return void 0===t&&(t=!0),1!==this._nodes.length||e?this.create("node",t?"inferredMrow":"mrow",this._nodes,{}):this.First},t.prototype.create=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.factory.configuration.nodeFactory).create.apply(e,a([t],r))},t}();e.MmlStack=u;var c=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=t.call(this,r)||this;return i.factory=e,i.global={},i._properties={},i.isOpen&&(i._env={}),i}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"base"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"env",{get:function(){return this._env},set:function(t){this._env=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.getProperty=function(t){return this._properties[t]},e.prototype.setProperty=function(t,e){return this._properties[t]=e,this},Object.defineProperty(e.prototype,"isOpen",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.isKind=function(t){return t===this.kind},e.prototype.checkItem=function(t){if(t.isKind("over")&&this.isOpen&&(t.setProperty("num",this.toMml(!1)),this.Clear()),t.isKind("cell")&&this.isOpen){if(t.getProperty("linebreak"))return e.fail;throw new l.default("Misplaced","Misplaced %1",t.getName())}if(t.isClose&&this.getErrors(t.kind)){var r=o(this.getErrors(t.kind),2),n=r[0],i=r[1];throw new l.default(n,i,t.getName())}return t.isFinal?(this.Push(t.First),e.fail):e.success},e.prototype.clearEnv=function(){var t,e;try{for(var r=s(Object.keys(this.env)),n=r.next();!n.done;n=r.next()){var i=n.value;delete this.env[i]}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.setProperties=function(t){return Object.assign(this._properties,t),this},e.prototype.getName=function(){return this.getProperty("name")},e.prototype.toString=function(){return this.kind+"["+this.nodes.join("; ")+"]"},e.prototype.getErrors=function(t){return(this.constructor.errors||{})[t]||e.errors[t]},e.fail=[null,!1],e.success=[null,!0],e.errors={end:["MissingBeginExtraEnd","Missing \\begin{%1} or extra \\end{%1}"],close:["ExtraCloseMissingOpen","Extra close brace or missing open brace"],right:["MissingLeftExtraRight","Missing \\left or extra \\right"]},e}(u);e.BaseItem=c},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.ParserConfiguration=e.ConfigurationHandler=e.Configuration=void 0;var o,a=r(8),s=r(7),l=r(27),u=r(26),c=r(6),p=function(){function t(t,e,r,n,i,o,a,s,l,u,c,p){void 0===e&&(e={}),void 0===r&&(r={}),void 0===n&&(n={}),void 0===i&&(i={}),void 0===o&&(o={}),void 0===a&&(a={}),void 0===s&&(s=[]),void 0===l&&(l=[]),void 0===u&&(u=null),void 0===c&&(c=null),this.name=t,this.handler=e,this.fallback=r,this.items=n,this.tags=i,this.options=o,this.nodes=a,this.preprocessors=s,this.postprocessors=l,this.initMethod=u,this.configMethod=c,this.priority=p,this.handler=Object.assign({character:[],delimiter:[],macro:[],environment:[]},e)}return t.makeProcessor=function(t,e){return Array.isArray(t)?t:[t,e]},t._create=function(e,r){var n=this;void 0===r&&(r={});var i=r.priority||u.PrioritizedList.DEFAULTPRIORITY,o=r.init?this.makeProcessor(r.init,i):null,a=r.config?this.makeProcessor(r.config,i):null,s=(r.preprocessors||[]).map((function(t){return n.makeProcessor(t,i)})),l=(r.postprocessors||[]).map((function(t){return n.makeProcessor(t,i)}));return new t(e,r.handler||{},r.fallback||{},r.items||{},r.tags||{},r.options||{},r.nodes||{},s,l,o,a,i)},t.create=function(e,r){void 0===r&&(r={});var n=t._create(e,r);return o.set(e,n),n},t.local=function(e){return void 0===e&&(e={}),t._create("",e)},Object.defineProperty(t.prototype,"init",{get:function(){return this.initMethod?this.initMethod[0]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"config",{get:function(){return this.configMethod?this.configMethod[0]:null},enumerable:!1,configurable:!0}),t}();e.Configuration=p,function(t){var e=new Map;t.set=function(t,r){e.set(t,r)},t.get=function(t){return e.get(t)},t.keys=function(){return e.keys()}}(o=e.ConfigurationHandler||(e.ConfigurationHandler={}));var f=function(){function t(t){var e,r,i,o;this.initMethod=new l.FunctionList,this.configMethod=new l.FunctionList,this.configurations=new u.PrioritizedList,this.handlers=new s.SubHandlers,this.items={},this.tags={},this.options={},this.nodes={};try{for(var a=n(t.slice().reverse()),c=a.next();!c.done;c=a.next()){var p=c.value;this.addPackage(p)}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}try{for(var f=n(this.configurations),h=f.next();!h.done;h=f.next()){var d=h.value,m=d.item,g=d.priority;this.append(m,g)}}catch(t){i={error:t}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(i)throw i.error}}}return t.prototype.init=function(){this.initMethod.execute(this)},t.prototype.config=function(t){var e,r;this.configMethod.execute(this,t);try{for(var i=n(this.configurations),o=i.next();!o.done;o=i.next()){var a=o.value;this.addFilters(t,a.item)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},t.prototype.addPackage=function(t){var e="string"==typeof t?t:t[0],r=o.get(e);r&&this.configurations.add(r,"string"==typeof t?r.priority:t[1])},t.prototype.add=function(t,e,r){var i,o;void 0===r&&(r={}),this.append(t),this.configurations.add(t,t.priority),this.init();var s=e.parseOptions;s.nodeFactory.setCreators(t.nodes);try{for(var l=n(Object.keys(t.items)),u=l.next();!u.done;u=l.next()){var p=u.value;s.itemFactory.setNodeClass(p,t.items[p])}}catch(t){i={error:t}}finally{try{u&&!u.done&&(o=l.return)&&o.call(l)}finally{if(i)throw i.error}}c.TagsFactory.addTags(t.tags),a.defaultOptions(s.options,t.options),a.userOptions(s.options,r),this.addFilters(e,t),t.config&&t.config(this,e)},t.prototype.append=function(t,e){e=e||t.priority,t.initMethod&&this.initMethod.add(t.initMethod[0],t.initMethod[1]),t.configMethod&&this.configMethod.add(t.configMethod[0],t.configMethod[1]),this.handlers.add(t.handler,t.fallback,e),Object.assign(this.items,t.items),Object.assign(this.tags,t.tags),a.defaultOptions(this.options,t.options),Object.assign(this.nodes,t.nodes)},t.prototype.addFilters=function(t,e){var r,o,a,s;try{for(var l=n(e.preprocessors),u=l.next();!u.done;u=l.next()){var c=i(u.value,2),p=c[0],f=c[1];t.preFilters.add(p,f)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}try{for(var h=n(e.postprocessors),d=h.next();!d.done;d=h.next()){var m=i(d.value,2),g=m[0];f=m[1];t.postFilters.add(g,f)}}catch(t){a={error:t}}finally{try{d&&!d.done&&(s=h.return)&&s.call(h)}finally{if(a)throw a.error}}},t}();e.ParserConfiguration=f},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.EnvironmentMap=e.CommandMap=e.MacroMap=e.DelimiterMap=e.CharacterMap=e.AbstractParseMap=e.RegExpMap=e.AbstractSymbolMap=void 0;var l=r(22),u=r(7),c=function(){function t(t,e){this._name=t,this._parser=e,u.MapHandler.register(this)}return Object.defineProperty(t.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),t.prototype.parserFor=function(t){return this.contains(t)?this.parser:null},t.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],i=this.parserFor(n),a=this.lookup(n);return i&&a?i(r,a)||!0:null},Object.defineProperty(t.prototype,"parser",{get:function(){return this._parser},set:function(t){this._parser=t},enumerable:!1,configurable:!0}),t}();e.AbstractSymbolMap=c;var p=function(t){function e(e,r,n){var i=t.call(this,e,r)||this;return i._regExp=n,i}return i(e,t),e.prototype.contains=function(t){return this._regExp.test(t)},e.prototype.lookup=function(t){return this.contains(t)?t:null},e}(c);e.RegExpMap=p;var f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.map=new Map,e}return i(e,t),e.prototype.lookup=function(t){return this.map.get(t)},e.prototype.contains=function(t){return this.map.has(t)},e.prototype.add=function(t,e){this.map.set(t,e)},e.prototype.remove=function(t){this.map.delete(t)},e}(c);e.AbstractParseMap=f;var h=function(t){function e(e,r,n){var i,s,u=t.call(this,e,r)||this;try{for(var c=a(Object.keys(n)),p=c.next();!p.done;p=c.next()){var f=p.value,h=n[f],d=o("string"==typeof h?[h,null]:h,2),m=d[0],g=d[1],y=new l.Symbol(f,m,g);u.add(f,y)}}catch(t){i={error:t}}finally{try{p&&!p.done&&(s=c.return)&&s.call(c)}finally{if(i)throw i.error}}return u}return i(e,t),e}(f);e.CharacterMap=h;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.parse=function(e){var r=o(e,2),n=r[0],i=r[1];return t.prototype.parse.call(this,[n,"\\"+i])},e}(h);e.DelimiterMap=d;var m=function(t){function e(e,r,n){var i,s,u=t.call(this,e,null)||this;try{for(var c=a(Object.keys(r)),p=c.next();!p.done;p=c.next()){var f=p.value,h=r[f],d=o("string"==typeof h?[h]:h),m=d[0],g=d.slice(1),y=new l.Macro(f,n[m],g);u.add(f,y)}}catch(t){i={error:t}}finally{try{p&&!p.done&&(s=c.return)&&s.call(c)}finally{if(i)throw i.error}}return u}return i(e,t),e.prototype.parserFor=function(t){var e=this.lookup(t);return e?e.func:null},e.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],i=this.lookup(n),a=this.parserFor(n);return i&&a?a.apply(void 0,s([r,i.symbol],i.args))||!0:null},e}(f);e.MacroMap=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],i=this.lookup(n),a=this.parserFor(n);if(!i||!a)return null;if(!a)return null;var l=r.currentCS;r.currentCS="\\"+n;var u=a.apply(void 0,s([r,"\\"+i.symbol],i.args));return r.currentCS=l,u||!0},e}(m);e.CommandMap=g;var y=function(t){function e(e,r,n,i){var o=t.call(this,e,n,i)||this;return o.parser=r,o}return i(e,t),e.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],i=this.lookup(n),a=this.parserFor(n);return i&&a?(this.parser(r,i.symbol,a,i.args),!0):null},e}(m);e.EnvironmentMap=y},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.EquationItem=e.EqnArrayItem=e.ArrayItem=e.DotsItem=e.NotItem=e.FnItem=e.MmlItem=e.CellItem=e.PositionItem=e.StyleItem=e.EndItem=e.BeginItem=e.RightItem=e.LeftItem=e.OverItem=e.SubsupItem=e.PrimeItem=e.CloseItem=e.OpenItem=e.StopItem=e.StartItem=void 0;var s=r(7),l=r(3),u=r(2),c=r(1),p=r(5),f=r(0),h=r(10),d=function(t){function e(e,r){var n=t.call(this,e)||this;return n.global=r,n}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"start"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("stop")){var r=this.toMml();return this.global.isInner||(r=this.factory.configuration.tags.finalize(r,this.env)),[[this.factory.create("mml",r)],!0]}return t.prototype.checkItem.call(this,e)},e}(h.BaseItem);e.StartItem=d;var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"stop"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(h.BaseItem);e.StopItem=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"open"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("close")){var r=this.toMml(),n=this.create("node","TeXAtom",[r]);return[[this.factory.create("mml",n)],!0]}return t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(h.BaseItem.errors),{stop:["ExtraOpenMissingClose","Extra open brace or missing close brace"]}),e}(h.BaseItem);e.OpenItem=g;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"close"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(h.BaseItem);e.CloseItem=y;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"prime"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){var e=o(this.Peek(2),2),r=e[0],n=e[1];return!f.default.isType(r,"msubsup")||f.default.isType(r,"msup")?[[this.create("node","msup",[r,n]),t],!0]:(f.default.setChild(r,r.sup,n),[[r,t],!0])},e}(h.BaseItem);e.PrimeItem=v;var b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"subsup"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("open")||e.isKind("left"))return h.BaseItem.success;var r=this.First,n=this.getProperty("position");if(e.isKind("mml")){if(this.getProperty("primes"))if(2!==n)f.default.setChild(r,2,this.getProperty("primes"));else{f.default.setProperty(this.getProperty("primes"),"variantForm",!0);var i=this.create("node","mrow",[this.getProperty("primes"),e.First]);e.First=i}return f.default.setChild(r,n,e.First),null!=this.getProperty("movesupsub")&&f.default.setProperty(r,"movesupsub",this.getProperty("movesupsub")),[[this.factory.create("mml",r)],!0]}if(t.prototype.checkItem.call(this,e)[1]){var o=this.getErrors(["","sub","sup"][n]);throw new(c.default.bind.apply(c.default,a([void 0,o[0],o[1]],o.splice(2))))}return null},e.errors=Object.assign(Object.create(h.BaseItem.errors),{stop:["MissingScript","Missing superscript or subscript argument"],sup:["MissingOpenForSup","Missing open brace for superscript"],sub:["MissingOpenForSub","Missing open brace for subscript"]}),e}(h.BaseItem);e.SubsupItem=b;var T=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("name","\\over"),r}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"over"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("over"))throw new c.default("AmbiguousUseOf","Ambiguous use of %1",e.getName());if(e.isClose){var r=this.create("node","mfrac",[this.getProperty("num"),this.toMml(!1)]);return null!=this.getProperty("thickness")&&f.default.setAttribute(r,"linethickness",this.getProperty("thickness")),(this.getProperty("open")||this.getProperty("close"))&&(f.default.setProperty(r,"withDelims",!0),r=p.default.fixedFence(this.factory.configuration,this.getProperty("open"),r,this.getProperty("close"))),[[this.factory.create("mml",r),e],!0]}return t.prototype.checkItem.call(this,e)},e.prototype.toString=function(){return"over["+this.getProperty("num")+" / "+this.nodes.join("; ")+"]"},e}(h.BaseItem);e.OverItem=T;var P=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("delim","("),r}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"left"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("right")?[[this.factory.create("mml",p.default.fenced(this.factory.configuration,this.getProperty("delim"),this.toMml(),e.getProperty("delim")))],!0]:t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(h.BaseItem.errors),{stop:["ExtraLeftMissingRight","Extra \\left or missing \\right"]}),e}(h.BaseItem);e.LeftItem=P;var S=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("delim",")"),r}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"right"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(h.BaseItem);e.RightItem=S;var x=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"begin"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){if(e.getName()!==this.getName())throw new c.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),e.getName());return this.getProperty("end")?h.BaseItem.fail:[[this.factory.create("mml",this.toMml())],!0]}if(e.isKind("stop"))throw new c.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(h.BaseItem);e.BeginItem=x;var A=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"end"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(h.BaseItem);e.EndItem=A;var M=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"style"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(!e.isClose)return t.prototype.checkItem.call(this,e);var r=this.create("node","mstyle",this.nodes,this.getProperty("styles"));return[[this.factory.create("mml",r),e],!0]},e}(h.BaseItem);e.StyleItem=M;var O=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"position"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isClose)throw new c.default("MissingBoxFor","Missing box for %1",this.getName());if(e.isFinal){var r=e.toMml();switch(this.getProperty("move")){case"vertical":return r=this.create("node","mpadded",[r],{height:this.getProperty("dh"),depth:this.getProperty("dd"),voffset:this.getProperty("dh")}),[[this.factory.create("mml",r)],!0];case"horizontal":return[[this.factory.create("mml",this.getProperty("left")),e,this.factory.create("mml",this.getProperty("right"))],!0]}}return t.prototype.checkItem.call(this,e)},e}(h.BaseItem);e.PositionItem=O;var C=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"cell"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(h.BaseItem);e.CellItem=C;var E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"kind",{get:function(){return"mml"},enumerable:!1,configurable:!0}),e}(h.BaseItem);e.MmlItem=E;var w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"fn"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){var r=this.First;if(r){if(e.isOpen)return h.BaseItem.success;if(!e.isKind("fn")){var n=e.First;if(!e.isKind("mml")||!n)return[[r,e],!0];if(f.default.isType(n,"mstyle")&&n.childNodes.length&&f.default.isType(n.childNodes[0].childNodes[0],"mspace")||f.default.isType(n,"mspace"))return[[r,e],!0];f.default.isEmbellished(n)&&(n=f.default.getCoreMO(n));var i=f.default.getForm(n);if(null!=i&&[0,0,1,1,0,1,1,0,0,0][i[2]])return[[r,e],!0]}var o=this.create("token","mo",{texClass:u.TEXCLASS.NONE},l.entities.ApplyFunction);return[[r,o,e],!0]}return t.prototype.checkItem.apply(this,arguments)},e}(h.BaseItem);e.FnItem=w;var k=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.remap=s.MapHandler.getMap("not_remap"),e}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"not"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){var e,r,n;if(t.isKind("open")||t.isKind("left"))return h.BaseItem.success;if(t.isKind("mml")&&(f.default.isType(t.First,"mo")||f.default.isType(t.First,"mi")||f.default.isType(t.First,"mtext"))&&(e=t.First,1===(r=f.default.getText(e)).length&&!f.default.getProperty(e,"movesupsub")&&1===f.default.getChildren(e).length))return this.remap.contains(r)?(n=this.create("text",this.remap.lookup(r).char),f.default.setChild(e,0,n)):(n=this.create("text","\u0338"),f.default.appendChildren(e,[n])),[[t],!0];n=this.create("text","\u29f8");var i=this.create("node","mtext",[],{},n),o=this.create("node","mpadded",[i],{width:0});return[[e=this.create("node","TeXAtom",[o],{texClass:u.TEXCLASS.REL}),t],!0]},e}(h.BaseItem);e.NotItem=k;var I=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"dots"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){if(t.isKind("open")||t.isKind("left"))return h.BaseItem.success;var e=this.getProperty("ldots"),r=t.First;if(t.isKind("mml")&&f.default.isEmbellished(r)){var n=f.default.getTexClass(f.default.getCoreMO(r));n!==u.TEXCLASS.BIN&&n!==u.TEXCLASS.REL||(e=this.getProperty("cdots"))}return[[e,t],!0]},e}(h.BaseItem);e.DotsItem=I;var _=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.table=[],e.row=[],e.frame=[],e.hfill=[],e.arraydef={},e.dashed=!1,e}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"array"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isClose&&!e.isKind("over")){if(e.getProperty("isEntry"))return this.EndEntry(),this.clearEnv(),h.BaseItem.fail;if(e.getProperty("isCR"))return this.EndEntry(),this.EndRow(),this.clearEnv(),h.BaseItem.fail;this.EndTable(),this.clearEnv();var r=this.arraydef.scriptlevel;delete this.arraydef.scriptlevel;var n=this.create("node","mtable",this.table,this.arraydef);4===this.frame.length?f.default.setAttribute(n,"frame",this.dashed?"dashed":"solid"):this.frame.length&&(this.arraydef.rowlines&&(this.arraydef.rowlines=this.arraydef.rowlines.replace(/none( none)+$/,"none")),n=this.create("node","menclose",[n],{notation:this.frame.join(" "),isFrame:!0}),"none"===(this.arraydef.columnlines||"none")&&"none"===(this.arraydef.rowlines||"none")||f.default.setAttribute(n,"padding",0)),r&&(n=this.create("node","mstyle",[n],{scriptlevel:r})),(this.getProperty("open")||this.getProperty("close"))&&(n=p.default.fenced(this.factory.configuration,this.getProperty("open"),n,this.getProperty("close")));var i=this.factory.create("mml",n);if(this.getProperty("requireClose")){if(e.isKind("close"))return[[i],!0];throw new c.default("MissingCloseBrace","Missing close brace")}return[[i,e],!0]}return t.prototype.checkItem.call(this,e)},e.prototype.EndEntry=function(){var t=this.create("node","mtd",this.nodes);this.hfill.length&&(0===this.hfill[0]&&f.default.setAttribute(t,"columnalign","right"),this.hfill[this.hfill.length-1]===this.Size()&&f.default.setAttribute(t,"columnalign",f.default.getAttribute(t,"columnalign")?"center":"left")),this.row.push(t),this.Clear(),this.hfill=[]},e.prototype.EndRow=function(){var t;this.getProperty("isNumbered")&&3===this.row.length?(this.row.unshift(this.row.pop()),t=this.create("node","mlabeledtr",this.row)):t=this.create("node","mtr",this.row),this.table.push(t),this.row=[]},e.prototype.EndTable=function(){(this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.checkLines()},e.prototype.checkLines=function(){if(this.arraydef.rowlines){var t=this.arraydef.rowlines.split(/ /);t.length===this.table.length?(this.frame.push("bottom"),t.pop(),this.arraydef.rowlines=t.join(" ")):t.length<this.table.length-1&&(this.arraydef.rowlines+=" none")}if(this.getProperty("rowspacing")){for(var e=this.arraydef.rowspacing.split(/ /);e.length<this.table.length;)e.push(this.getProperty("rowspacing")+"em");this.arraydef.rowspacing=e.join(" ")}},e}(h.BaseItem);e.ArrayItem=_;var L=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=t.call(this,e)||this;return i.factory.configuration.tags.start(r[0],r[2],r[1]),i}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"eqnarray"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){this.row.length&&p.default.fixInitialMO(this.factory.configuration,this.nodes);var t=this.create("node","mtd",this.nodes);this.row.push(t),this.Clear()},e.prototype.EndRow=function(){var t="mtr",e=this.factory.configuration.tags.getTag();e&&(this.row=[e].concat(this.row),t="mlabeledtr"),this.factory.configuration.tags.clearTag();var r=this.create("node",t,this.row);this.table.push(r),this.row=[]},e.prototype.EndTable=function(){t.prototype.EndTable.call(this),this.factory.configuration.tags.end()},e}(_);e.EqnArrayItem=L;var N=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=t.call(this,e)||this;return i.factory.configuration.tags.start("equation",!0,r[0]),i}return i(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"equation"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){var r=this.toMml(),n=this.factory.configuration.tags.getTag();return this.factory.configuration.tags.end(),[[n?this.factory.configuration.tags.enTag(r,n):r,e],!0]}if(e.isKind("stop"))throw new c.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(h.BaseItem);e.EquationItem=N},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.TeX=void 0;var s=r(29),l=r(8),u=r(15),c=r(16),p=r(0),f=r(4),h=r(1),d=r(18),m=r(6),g=r(11);r(21);var y=function(t){function e(r){void 0===r&&(r={});var n=this,i=a(l.separateOptions(r,e.OPTIONS,u.FindTeX.OPTIONS),3),o=i[0],s=i[1],p=i[2];(n=t.call(this,s)||this).findTeX=n.options.FindTeX||new u.FindTeX(p);var f=n.options.packages,h=n.configuration=e.configure(f),g=n._parseOptions=new d.default(h,[n.options,m.TagsFactory.OPTIONS]);return l.userOptions(g.options,o),h.config(n),e.tags(g,h),n.postFilters.add(c.default.cleanSubSup,-6),n.postFilters.add(c.default.setInherited,-5),n.postFilters.add(c.default.moveLimits,-4),n.postFilters.add(c.default.cleanStretchy,-3),n.postFilters.add(c.default.cleanAttributes,-2),n.postFilters.add(c.default.combineRelations,-1),n}return i(e,t),e.configure=function(t){var e=new g.ParserConfiguration(t);return e.init(),e},e.tags=function(t,e){m.TagsFactory.addTags(e.tags),m.TagsFactory.setDefault(t.options.tags),t.tags=m.TagsFactory.getDefault(),t.tags.configuration=t},e.prototype.setMmlFactory=function(e){t.prototype.setMmlFactory.call(this,e),this._parseOptions.nodeFactory.setMmlFactory(e)},Object.defineProperty(e.prototype,"parseOptions",{get:function(){return this._parseOptions},enumerable:!1,configurable:!0}),e.prototype.compile=function(t,e){this.parseOptions.clear(),this.executeFilters(this.preFilters,t,e,this.parseOptions);var r,n=t.display;this.latex=t.math,this.parseOptions.tags.startEquation(t);try{r=new f.default(this.latex,{display:n,isInner:!1},this.parseOptions).mml()}catch(t){if(!(t instanceof h.default))throw t;this.parseOptions.error=!0,r=this.options.formatError(this,t)}return r=this.parseOptions.nodeFactory.create("node","math",[r]),n&&p.default.setAttribute(r,"display","block"),this.parseOptions.tags.finishEquation(t),this.parseOptions.root=r,this.executeFilters(this.postFilters,t,e,this.parseOptions),this.mathNode=this.parseOptions.root,this.mathNode},e.prototype.findMath=function(t){return this.findTeX.findMath(t)},e.prototype.formatError=function(t){var e=t.message.replace(/\n.*/,"");return this.parseOptions.nodeFactory.create("error",e,t.id,this.latex)},e.NAME="TeX",e.OPTIONS=o(o({},s.AbstractInputJax.OPTIONS),{FindTeX:null,packages:["base"],digits:/^(?:[0-9]+(?:\{,\}[0-9]{3})*(?:\.[0-9]*)?|\.[0-9]+)/,maxBuffer:5120,formatError:function(t,e){return t.formatError(e)}}),e}(s.AbstractInputJax);e.TeX=y},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.FindTeX=void 0;var a=r(30),s=r(31),l=r(32),u=function(t){function e(e){var r=t.call(this,e)||this;return r.getPatterns(),r}return i(e,t),e.prototype.getPatterns=function(){var t=this,e=this.options,r=[],n=[],i=[];this.end={},this.env=this.sub=0;var o=1;e.inlineMath.forEach((function(e){return t.addPattern(r,e,!1)})),e.displayMath.forEach((function(e){return t.addPattern(r,e,!0)})),r.length&&n.push(r.sort(s.sortLength).join("|")),e.processEnvironments&&(n.push("\\\\begin\\s*\\{([^}]*)\\}"),this.env=o,o++),e.processEscapes&&i.push("\\\\([\\\\$])"),e.processRefs&&i.push("(\\\\(?:eq)?ref\\s*\\{[^}]*\\})"),i.length&&(n.push("("+i.join("|")+")"),this.sub=o),this.start=new RegExp(n.join("|"),"g"),this.hasPatterns=n.length>0},e.prototype.addPattern=function(t,e,r){var n=o(e,2),i=n[0],a=n[1];t.push(s.quotePattern(i)),this.end[i]=[a,r,this.endPattern(a)]},e.prototype.endPattern=function(t,e){return new RegExp((e||s.quotePattern(t))+"|\\\\(?:[a-zA-Z]|.)|[{}]","g")},e.prototype.findEnd=function(t,e,r,n){for(var i,a=o(n,3),s=a[0],u=a[1],c=a[2],p=c.lastIndex=r.index+r[0].length,f=0;i=c.exec(t);){if((i[1]||i[0])===s&&0===f)return l.protoItem(r[0],t.substr(p,i.index-p),i[0],e,r.index,i.index+i[0].length,u);"{"===i[0]?f++:"}"===i[0]&&f&&f--}return null},e.prototype.findMathInString=function(t,e,r){var n,i;for(this.start.lastIndex=0;n=this.start.exec(r);){if(void 0!==n[this.env]&&this.env){var o="\\\\end\\s*(\\{"+s.quotePattern(n[this.env])+"\\})";(i=this.findEnd(r,e,n,["{"+n[this.env]+"}",!0,this.endPattern(null,o)]))&&(i.math=i.open+i.math+i.close,i.open=i.close="")}else if(void 0!==n[this.sub]&&this.sub){var a=n[this.sub];o=n.index+n[this.sub].length;i=2===a.length?l.protoItem("",a.substr(1),"",e,n.index,o):l.protoItem("",a,"",e,n.index,o,!1)}else i=this.findEnd(r,e,n,this.end[n[0]]);i&&(t.push(i),this.start.lastIndex=i.end.n)}},e.prototype.findMath=function(t){var e=[];if(this.hasPatterns)for(var r=0,n=t.length;r<n;r++)this.findMathInString(e,r,t[r]);return e},e.OPTIONS={inlineMath:[["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],processEscapes:!0,processEnvironments:!0,processRefs:!0},e}(a.AbstractFindMath);e.FindTeX=u},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var i,o=r(2),a=r(0);!function(t){t.cleanStretchy=function(t){var e,r,i=t.data;try{for(var o=n(i.getList("fixStretchy")),s=o.next();!s.done;s=o.next()){var l=s.value;if(a.default.getProperty(l,"fixStretchy")){var u=a.default.getForm(l);u&&u[3]&&u[3].stretchy&&a.default.setAttribute(l,"stretchy",!1);var c=l.parent;if(!(a.default.getTexClass(l)||u&&u[2])){var p=i.nodeFactory.create("node","TeXAtom",[l]);c.replaceChild(p,l),p.inheritAttributesFrom(l)}a.default.removeProperties(l,"fixStretchy")}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.cleanAttributes=function(t){t.data.root.walkTree((function(t,e){var r,i,o=t.attributes;if(o)try{for(var a=n(o.getExplicitNames()),s=a.next();!s.done;s=a.next()){var l=s.value;o.attributes[l]===t.attributes.getInherited(l)&&delete o.attributes[l]}}catch(t){r={error:t}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(r)throw r.error}}}),{})},t.combineRelations=function(t){var i,s;try{for(var l=n(t.data.getList("mo")),u=l.next();!u.done;u=l.next()){var c=u.value;if(!c.getProperty("relationsCombined")&&c.parent&&(!c.parent||a.default.isType(c.parent,"mrow"))&&a.default.getTexClass(c)===o.TEXCLASS.REL){for(var p=c.parent,f=void 0,h=p.childNodes,d=h.indexOf(c)+1,m=a.default.getProperty(c,"variantForm");d<h.length&&(f=h[d])&&a.default.isType(f,"mo")&&a.default.getTexClass(f)===o.TEXCLASS.REL;){if(m!==a.default.getProperty(f,"variantForm")||!r(c,f)){null==c.attributes.getExplicit("rspace")&&a.default.setAttribute(c,"rspace","0pt"),null==f.attributes.getExplicit("lspace")&&a.default.setAttribute(f,"lspace","0pt");break}a.default.appendChildren(c,a.default.getChildren(f)),e(["stretchy","rspace"],c,f),a.default.setProperties(c,f.getAllProperties()),h.splice(d,1),f.parent=null,f.setProperty("relationsCombined",!0)}c.attributes.setInherited("form",c.getForms()[0])}}}catch(t){i={error:t}}finally{try{u&&!u.done&&(s=l.return)&&s.call(l)}finally{if(i)throw i.error}}};var e=function(t,e,r){var n=e.attributes,i=r.attributes;t.forEach((function(t){var e=i.getExplicit(t);null!=e&&n.set(t,e)}))},r=function(t,e){var r,i,o=function(t,e){return t.getExplicitNames().filter((function(r){return r!==e&&("stretchy"!==r||t.getExplicit("stretchy"))}))},a=t.attributes,s=e.attributes,l=o(a,"lspace"),u=o(s,"rspace");if(l.length!==u.length)return!1;try{for(var c=n(l),p=c.next();!p.done;p=c.next()){var f=p.value;if(a.getExplicit(f)!==s.getExplicit(f))return!1}}catch(t){r={error:t}}finally{try{p&&!p.done&&(i=c.return)&&i.call(c)}finally{if(r)throw r.error}}return!0},i=function(t,e,r){var i,o;try{for(var s=n(t.getList("m"+e+r)),l=s.next();!l.done;l=s.next()){var u=l.value,c=u.childNodes;if(!c[u[e]]||!c[u[r]]){var p=u.parent,f=c[u[e]]?t.nodeFactory.create("node","m"+e,[c[u.base],c[u[e]]]):t.nodeFactory.create("node","m"+r,[c[u.base],c[u[r]]]);a.default.copyAttributes(u,f),p?p.replaceChild(f,u):t.root=f}}}catch(t){i={error:t}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(i)throw i.error}}};t.cleanSubSup=function(t){var e=t.data;e.error||(i(e,"sub","sup"),i(e,"under","over"))};var s=function(t,e,r){var i,o;try{for(var s=n(t.getList(e)),l=s.next();!l.done;l=s.next()){var u=l.value;if(!u.attributes.get("displaystyle")){var c=u.childNodes[u.base],p=c.coreMO();if(c.getProperty("movablelimits")&&!p.attributes.getExplicit("movablelimits")){var f=t.nodeFactory.create("node",r,u.childNodes);a.default.copyAttributes(u,f),u.parent?u.parent.replaceChild(f,u):t.root=f}}}}catch(t){i={error:t}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(i)throw i.error}}};t.moveLimits=function(t){var e=t.data;s(e,"munderover","msubsup"),s(e,"munder","msub"),s(e,"mover","msup")},t.setInherited=function(t){t.data.root.setInheritedAttributes({},t.math.display,0,!1)}}(i||(i={})),e.default=i},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var a=r(0),s=function(){function t(t,e,r){this._factory=t,this._env=e,this.global={},this.stack=[],this.global={isInner:r},this.stack=[this._factory.create("start",this.global)],e&&(this.stack[0].env=e),this.env=this.stack[0].env}return Object.defineProperty(t.prototype,"env",{get:function(){return this._env},set:function(t){this._env=t},enumerable:!1,configurable:!0}),t.prototype.Push=function(){for(var t,e,r=[],s=0;s<arguments.length;s++)r[s]=arguments[s];try{for(var l=n(r),u=l.next();!u.done;u=l.next()){var c=u.value;if(c){var p=a.default.isNode(c)?this._factory.create("mml",c):c;p.global=this.global;var f=i(this.stack.length?this.Top().checkItem(p):[null,!0],2),h=f[0],d=f[1];d&&(h?(this.Pop(),this.Push.apply(this,o(h))):(this.stack.push(p),p.env?(p.copyEnv&&Object.assign(p.env,this.env),this.env=p.env):p.env=this.env))}}}catch(e){t={error:e}}finally{try{u&&!u.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}},t.prototype.Pop=function(){var t=this.stack.pop();return t.isOpen||delete t.env,this.env=this.stack.length?this.Top().env:{},t},t.prototype.Top=function(t){return void 0===t&&(t=1),this.stack.length<t?null:this.stack[this.stack.length-t]},t.prototype.Prev=function(t){var e=this.Top();return t?e.First:e.Pop()},t.prototype.toString=function(){return"stack[\n  "+this.stack.join("\n  ")+"\n]"},t}();e.default=s},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var a=r(19),s=r(20),l=r(8),u=function(){function t(t,e){void 0===e&&(e=[]),this.options={},this.packageData=new Map,this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.handlers=t.handlers,this.nodeFactory=new s.NodeFactory,this.nodeFactory.configuration=this,this.nodeFactory.setCreators(t.nodes),this.itemFactory=new a.default(t.items),this.itemFactory.configuration=this,l.defaultOptions.apply(void 0,i([this.options],e)),l.defaultOptions(this.options,t.options)}return t.prototype.pushParser=function(t){this.parsers.unshift(t)},t.prototype.popParser=function(){this.parsers.shift()},Object.defineProperty(t.prototype,"parser",{get:function(){return this.parsers[0]},enumerable:!1,configurable:!0}),t.prototype.clear=function(){this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.tags.resetTag()},t.prototype.addNode=function(t,e){var r=this.nodeLists[t];r||(r=this.nodeLists[t]=[]),r.push(e)},t.prototype.getList=function(t){var e,r,n=this.nodeLists[t]||[],i=[];try{for(var a=o(n),s=a.next();!s.done;s=a.next()){var l=s.value;this.inTree(l)&&i.push(l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return this.nodeLists[t]=i,i},t.prototype.inTree=function(t){for(;t&&t!==this.root;)t=t.parent;return!!t},t}();e.default=u},function(t,e,r){"use strict";var n,i,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var a=r(10),s=r(34),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(a.BaseItem),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.defaultKind="dummy",e.configuration=null,e}return o(e,t),e.DefaultStackItems=((i={})[l.prototype.kind]=l,i),e}(s.AbstractFactory);e.default=u},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.NodeFactory=void 0;var o=r(0),a=function(){function t(){this.mmlFactory=null,this.factory={node:t.createNode,token:t.createToken,text:t.createText,error:t.createError}}return t.createNode=function(t,e,r,n,i){void 0===r&&(r=[]),void 0===n&&(n={});var a=t.mmlFactory.create(e),s=a.arity;if(s===1/0||-1===s)1===r.length&&r[0].isInferred?a.setChildren(o.default.getChildren(r[0])):a.setChildren(r);else{for(var l=[],u=0,c=void 0;c=r[u];u++)if(c.isInferred){var p=t.mmlFactory.create("mrow",{},o.default.getChildren(c));o.default.copyAttributes(c,p),l.push(p)}else l.push(c);a.setChildren(l)}return i&&a.appendChild(i),o.default.setProperties(a,n),a},t.createToken=function(t,e,r,n){void 0===r&&(r={}),void 0===n&&(n="");var i=t.create("text",n);return t.create("node",e,[],r,i)},t.createText=function(t,e){return null==e?null:t.mmlFactory.create("text").setText(e)},t.createError=function(t,e){var r=t.create("text",e),n=t.create("node","mtext",[],{},r);return t.create("node","merror",[n],{"data-mjx-error":e})},t.prototype.setMmlFactory=function(t){this.mmlFactory=t},t.prototype.set=function(t,e){this.factory[t]=e},t.prototype.setCreators=function(t){for(var e in t)this.set(e,t[e])},t.prototype.create=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=this.factory[t]||this.factory.node,o=n.apply(void 0,i([this,e[0]],e.slice(1)));return"node"===t&&this.configuration.addNode(e[0],o),o},t.prototype.get=function(t){return this.factory[t]},t}();e.NodeFactory=a},function(t,e,r){"use strict";var n,i,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BaseConfiguration=e.BaseTags=e.Other=void 0;var a=r(11),s=r(7),l=r(1),u=r(0),c=r(12),p=r(13),f=r(6);function h(t,e){var r=t.stack.env.font?{mathvariant:t.stack.env.font}:{},n=s.MapHandler.getMap("remap").lookup(e),i=t.create("token","mo",r,n?n.char:e);u.default.setProperty(i,"fixStretchy",!0),t.configuration.addNode("fixStretchy",i),t.Push(i)}r(35),new c.CharacterMap("remap",null,{"-":"\u2212","*":"\u2217","`":"\u2018"}),e.Other=h;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(f.AbstractTags);e.BaseTags=d,e.BaseConfiguration=a.Configuration.create("base",{handler:{character:["command","special","letter","digit"],delimiter:["delimiter"],macro:["delimiter","macros","mathchar0mi","mathchar0mo","mathchar7"],environment:["environment"]},fallback:{character:h,macro:function(t,e){throw new l.default("UndefinedControlSequence","Undefined control sequence %1","\\"+e)},environment:function(t,e){throw new l.default("UnknownEnv","Unknown environment '%1'",e)}},items:(i={},i[p.StartItem.prototype.kind]=p.StartItem,i[p.StopItem.prototype.kind]=p.StopItem,i[p.OpenItem.prototype.kind]=p.OpenItem,i[p.CloseItem.prototype.kind]=p.CloseItem,i[p.PrimeItem.prototype.kind]=p.PrimeItem,i[p.SubsupItem.prototype.kind]=p.SubsupItem,i[p.OverItem.prototype.kind]=p.OverItem,i[p.LeftItem.prototype.kind]=p.LeftItem,i[p.RightItem.prototype.kind]=p.RightItem,i[p.BeginItem.prototype.kind]=p.BeginItem,i[p.EndItem.prototype.kind]=p.EndItem,i[p.StyleItem.prototype.kind]=p.StyleItem,i[p.PositionItem.prototype.kind]=p.PositionItem,i[p.CellItem.prototype.kind]=p.CellItem,i[p.MmlItem.prototype.kind]=p.MmlItem,i[p.FnItem.prototype.kind]=p.FnItem,i[p.NotItem.prototype.kind]=p.NotItem,i[p.DotsItem.prototype.kind]=p.DotsItem,i[p.ArrayItem.prototype.kind]=p.ArrayItem,i[p.EqnArrayItem.prototype.kind]=p.EqnArrayItem,i[p.EquationItem.prototype.kind]=p.EquationItem,i),options:{maxMacros:1e3,baseURL:"undefined"==typeof document||0===document.getElementsByTagName("base").length?"":String(document.location).replace(/#.*$/,"")},tags:{base:d}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Macro=e.Symbol=void 0;var n=function(){function t(t,e,r){this._symbol=t,this._char=e,this._attributes=r}return Object.defineProperty(t.prototype,"symbol",{get:function(){return this._symbol},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"char",{get:function(){return this._char},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){return this._attributes},enumerable:!1,configurable:!0}),t}();e.Symbol=n;var i=function(){function t(t,e,r){void 0===r&&(r=[]),this._symbol=t,this._func=e,this._args=r}return Object.defineProperty(t.prototype,"symbol",{get:function(){return this._symbol},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"func",{get:function(){return this._func},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"args",{get:function(){return this._args},enumerable:!1,configurable:!0}),t}();e.Macro=i},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var i=r(13),o=r(0),a=r(1),s=r(4),l=r(9),u=r(5),c=r(2),p=r(6),f=r(3);r(25),r(36),r(37);var h={},d={fontfamily:1,fontsize:1,fontweight:1,fontstyle:1,color:1,background:1,id:1,class:1,href:1,style:1};function m(t,e){var r=t.stack.env,n=r.inRoot;r.inRoot=!0;var i=new s.default(e,r,t.configuration),o=i.mml(),a=i.stack.global;if(a.leftRoot||a.upRoot){var l={};a.leftRoot&&(l.width=a.leftRoot),a.upRoot&&(l.voffset=a.upRoot,l.height=a.upRoot),o=t.create("node","mpadded",[o],l)}return r.inRoot=n,o}h.Open=function(t,e){t.Push(t.itemFactory.create("open"))},h.Close=function(t,e){t.Push(t.itemFactory.create("close"))},h.Tilde=function(t,e){t.Push(t.create("token","mtext",{},f.entities.nbsp))},h.Space=function(t,e){},h.Superscript=function(t,e){var r,i,s;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var l=t.stack.Top();l.isKind("prime")?(s=(r=n(l.Peek(2),2))[0],i=r[1],t.stack.Pop()):(s=t.stack.Prev())||(s=t.create("token","mi",{},""));var u=o.default.getProperty(s,"movesupsub"),c=o.default.isType(s,"msubsup")?s.sup:s.over;if(o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")&&o.default.getChildAt(s,s.sup)||o.default.isType(s,"munderover")&&!o.default.isType(s,"mover")&&o.default.getChildAt(s,s.over)&&!o.default.getProperty(s,"subsupOK"))throw new a.default("DoubleExponent","Double exponent: use braces to clarify");o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")||(u?((!o.default.isType(s,"munderover")||o.default.isType(s,"mover")||o.default.getChildAt(s,s.over))&&(s=t.create("node","munderover",[s],{movesupsub:!0})),c=s.over):c=(s=t.create("node","msubsup",[s])).sup),t.Push(t.itemFactory.create("subsup",s).setProperties({position:c,primes:i,movesupsub:u}))},h.Subscript=function(t,e){var r,i,s;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var l=t.stack.Top();l.isKind("prime")?(s=(r=n(l.Peek(2),2))[0],i=r[1],t.stack.Pop()):(s=t.stack.Prev())||(s=t.create("token","mi",{},""));var u=o.default.getProperty(s,"movesupsub"),c=o.default.isType(s,"msubsup")?s.sub:s.under;if(o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")&&o.default.getChildAt(s,s.sub)||o.default.isType(s,"munderover")&&!o.default.isType(s,"mover")&&o.default.getChildAt(s,s.under)&&!o.default.getProperty(s,"subsupOK"))throw new a.default("DoubleSubscripts","Double subscripts: use braces to clarify");o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")||(u?((!o.default.isType(s,"munderover")||o.default.isType(s,"mover")||o.default.getChildAt(s,s.under))&&(s=t.create("node","munderover",[s],{movesupsub:!0})),c=s.under):c=(s=t.create("node","msubsup",[s])).sub),t.Push(t.itemFactory.create("subsup",s).setProperties({position:c,primes:i,movesupsub:u}))},h.Prime=function(t,e){var r=t.stack.Prev();if(r||(r=t.create("node","mi")),o.default.isType(r,"msubsup")&&!o.default.isType(r,"msup")&&o.default.getChildAt(r,r.sup))throw new a.default("DoubleExponentPrime","Prime causes double exponent: use braces to clarify");var n="";t.i--;do{n+=f.entities.prime,t.i++,e=t.GetNext()}while("'"===e||e===f.entities.rquote);n=["","\u2032","\u2033","\u2034","\u2057"][n.length]||n;var i=t.create("token","mo",{},n);t.Push(t.itemFactory.create("prime",r,i))},h.Comment=function(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++},h.Hash=function(t,e){throw new a.default("CantUseHash1","You can't use 'macro parameter character #' in math mode")},h.SetFont=function(t,e,r){t.stack.env.font=r},h.SetStyle=function(t,e,r,n,i){t.stack.env.style=r,t.stack.env.level=i,t.Push(t.itemFactory.create("style").setProperty("styles",{displaystyle:n,scriptlevel:i}))},h.SetSize=function(t,e,r){t.stack.env.size=r,t.Push(t.itemFactory.create("style").setProperty("styles",{mathsize:r+"em"}))},h.Spacer=function(t,e,r){var n=t.create("node","mspace",[],{width:r}),i=t.create("node","mstyle",[n],{scriptlevel:0});t.Push(i)},h.LeftRight=function(t,e){var r=e.substr(1);t.Push(t.itemFactory.create(r).setProperty("delim",t.GetDelimiter(e)))},h.Middle=function(t,e){var r=t.GetDelimiter(e),n=t.create("node","TeXAtom",[],{texClass:c.TEXCLASS.CLOSE});if(t.Push(n),!t.stack.Top().isKind("left"))throw new a.default("MisplacedMiddle","%1 must be within \\left and \\right",t.currentCS);n=t.create("token","mo",{stretchy:!0},r),t.Push(n),n=t.create("node","TeXAtom",[],{texClass:c.TEXCLASS.OPEN}),t.Push(n)},h.NamedFn=function(t,e,r){r||(r=e.substr(1));var n=t.create("token","mi",{texClass:c.TEXCLASS.OP},r);t.Push(t.itemFactory.create("fn",n))},h.NamedOp=function(t,e,r){r||(r=e.substr(1)),r=r.replace(/&thinsp;/,"\u2006");var n=t.create("token","mo",{movablelimits:!0,movesupsub:!0,form:l.TexConstant.Form.PREFIX,texClass:c.TEXCLASS.OP},r);t.Push(n)},h.Limits=function(t,e,r){var n=t.stack.Prev(!0);if(!n||o.default.getTexClass(o.default.getCoreMO(n))!==c.TEXCLASS.OP&&null==o.default.getProperty(n,"movesupsub"))throw new a.default("MisplacedLimits","%1 is allowed only on operators",t.currentCS);var i,s=t.stack.Top();o.default.isType(n,"munderover")&&!r?(i=t.create("node","msubsup"),o.default.copyChildren(n,i),n=s.Last=i):o.default.isType(n,"msubsup")&&r&&(i=t.create("node","munderover"),o.default.copyChildren(n,i),n=s.Last=i),o.default.setProperty(n,"movesupsub",!!r),o.default.setProperties(o.default.getCoreMO(n),{movablelimits:!1}),(o.default.getAttribute(n,"movablelimits")||o.default.getProperty(n,"movablelimits"))&&o.default.setProperties(n,{movablelimits:!1})},h.Over=function(t,e,r,n){var i=t.itemFactory.create("over").setProperty("name",t.currentCS);r||n?(i.setProperty("open",r),i.setProperty("close",n)):e.match(/withdelims$/)&&(i.setProperty("open",t.GetDelimiter(e)),i.setProperty("close",t.GetDelimiter(e))),e.match(/^\\above/)?i.setProperty("thickness",t.GetDimen(e)):(e.match(/^\\atop/)||r||n)&&i.setProperty("thickness",0),t.Push(i)},h.Frac=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e),i=t.create("node","mfrac",[r,n]);t.Push(i)},h.Sqrt=function(t,e){var r=t.GetBrackets(e),n=t.GetArgument(e);"\\frac"===n&&(n+="{"+t.GetArgument(n)+"}{"+t.GetArgument(n)+"}");var i=new s.default(n,t.stack.env,t.configuration).mml();i=r?t.create("node","mroot",[i,m(t,r)]):t.create("node","msqrt",[i]),t.Push(i)},h.Root=function(t,e){var r=t.GetUpTo(e,"\\of"),n=t.ParseArg(e),i=t.create("node","mroot",[n,m(t,r)]);t.Push(i)},h.MoveRoot=function(t,e,r){if(!t.stack.env.inRoot)throw new a.default("MisplacedMoveRoot","%1 can appear only within a root",t.currentCS);if(t.stack.global[r])throw new a.default("MultipleMoveRoot","Multiple use of %1",t.currentCS);var n=t.GetArgument(e);if(!n.match(/-?[0-9]+/))throw new a.default("IntegerArg","The argument to %1 must be an integer",t.currentCS);"-"!==(n=parseInt(n,10)/15+"em").substr(0,1)&&(n="+"+n),t.stack.global[r]=n},h.Accent=function(t,e,r,n){var i=t.ParseArg(e),a=u.default.getFontDef(t);a.accent=!0;var s=o.default.createEntity(r),l=t.create("token","mo",a,s);o.default.setAttribute(l,"stretchy",!!n);var c=o.default.isEmbellished(i)?o.default.getCoreMO(i):i;o.default.isType(c,"mo")&&o.default.setProperties(c,{movablelimits:!1});var p=t.create("node","munderover");o.default.setChild(p,0,i),o.default.setChild(p,1,null),o.default.setChild(p,2,l);var f=t.create("node","TeXAtom",[p]);t.Push(f)},h.UnderOver=function(t,e,r,n,i){var a,s=t.ParseArg(e),l=o.default.getForm(s);(l&&l[3]&&l[3].movablelimits||o.default.getProperty(s,"movablelimits"))&&o.default.setProperties(s,{movablelimits:!1}),o.default.isType(s,"munderover")&&o.default.isEmbellished(s)&&(o.default.setProperties(o.default.getCoreMO(s),{lspace:0,rspace:0}),a=t.create("node","mo",[],{rspace:0}),s=t.create("node","mrow",[a,s]));var u=t.create("node","munderover",[s]),p=o.default.createEntity(r);a=t.create("token","mo",{stretchy:!0,accent:!i},p),o.default.setChild(u,"o"===e.charAt(1)?u.over:u.under,a);var f=u;n&&(f=t.create("node","TeXAtom",[u],{texClass:c.TEXCLASS.OP,movesupsub:!0})),o.default.setProperty(f,"subsupOK",!0),t.Push(f)},h.Overset=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);(o.default.getAttribute(n,"movablelimits")||o.default.getProperty(n,"movablelimits"))&&o.default.setProperties(n,{movablelimits:!1});var i=t.create("node","mover",[n,r]);t.Push(i)},h.Underset=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);(o.default.isType(n,"mo")||o.default.getProperty(n,"movablelimits"))&&o.default.setProperties(n,{movablelimits:!1});var i=t.create("node","munder",[n,r]);t.Push(i)},h.TeXAtom=function(t,e,r){var n,i,o,a={texClass:r};if(r===c.TEXCLASS.OP){a.movesupsub=a.movablelimits=!0;var u=t.GetArgument(e),p=u.match(/^\s*\\rm\s+([a-zA-Z0-9 ]+)$/);p?(a.mathvariant=l.TexConstant.Variant.NORMAL,i=t.create("token","mi",a,p[1])):(o=new s.default(u,t.stack.env,t.configuration).mml(),i=t.create("node","TeXAtom",[o],a)),n=t.itemFactory.create("fn",i)}else o=t.ParseArg(e),n=t.create("node","TeXAtom",[o],a);t.Push(n)},h.MmlToken=function(t,e){var r,n=t.GetArgument(e),i=t.GetBrackets(e,"").replace(/^\s+/,""),s=t.GetArgument(e),l={};try{r=t.create("node",n)}catch(t){r=null}if(!r||!r.isToken)throw new a.default("NotMathMLToken","%1 is not a token element",n);for(;""!==i;){var c=i.match(/^([a-z]+)\s*=\s*('[^']*'|"[^"]*"|[^ ,]*)\s*,?\s*/i);if(!c)throw new a.default("InvalidMathMLAttr","Invalid MathML attribute: %1",i);if(!r.attributes.hasDefault(c[1])&&!d[c[1]])throw new a.default("UnknownAttrForElement","%1 is not a recognized attribute for %2",c[1],n);var p=u.default.MmlFilterAttribute(t,c[1],c[2].replace(/^(['"])(.*)\1$/,"$2"));p&&("true"===p.toLowerCase()?p=!0:"false"===p.toLowerCase()&&(p=!1),l[c[1]]=p),i=i.substr(c[0].length)}var f=t.create("text",s);r.appendChild(f),o.default.setProperties(r,l),t.Push(r)},h.Strut=function(t,e){var r=t.create("node","mrow"),n=t.create("node","mpadded",[r],{height:"8.6pt",depth:"3pt",width:0});t.Push(n)},h.Phantom=function(t,e,r,n){var i=t.create("node","mphantom",[t.ParseArg(e)]);(r||n)&&(i=t.create("node","mpadded",[i]),n&&(o.default.setAttribute(i,"height",0),o.default.setAttribute(i,"depth",0)),r&&o.default.setAttribute(i,"width",0));var a=t.create("node","TeXAtom",[i]);t.Push(a)},h.Smash=function(t,e){var r=u.default.trimSpaces(t.GetBrackets(e,"")),n=t.create("node","mpadded",[t.ParseArg(e)]);switch(r){case"b":o.default.setAttribute(n,"depth",0);break;case"t":o.default.setAttribute(n,"height",0);break;default:o.default.setAttribute(n,"height",0),o.default.setAttribute(n,"depth",0)}var i=t.create("node","TeXAtom",[n]);t.Push(i)},h.Lap=function(t,e){var r=t.create("node","mpadded",[t.ParseArg(e)],{width:0});"\\llap"===e&&o.default.setAttribute(r,"lspace","-1width");var n=t.create("node","TeXAtom",[r]);t.Push(n)},h.RaiseLower=function(t,e){var r=t.GetDimen(e),n=t.itemFactory.create("position").setProperties({name:t.currentCS,move:"vertical"});"-"===r.charAt(0)&&(r=r.slice(1),e="raise"===e.substr(1)?"\\lower":"\\raise"),"\\lower"===e?(n.setProperty("dh","-"+r),n.setProperty("dd","+"+r)):(n.setProperty("dh","+"+r),n.setProperty("dd","-"+r)),t.Push(n)},h.MoveLeftRight=function(t,e){var r=t.GetDimen(e),n="-"===r.charAt(0)?r.slice(1):"-"+r;if("\\moveleft"===e){var i=r;r=n,n=i}t.Push(t.itemFactory.create("position").setProperties({name:t.currentCS,move:"horizontal",left:t.create("node","mspace",[],{width:r}),right:t.create("node","mspace",[],{width:n})}))},h.Hskip=function(t,e){var r=t.create("node","mspace",[],{width:t.GetDimen(e)});t.Push(r)},h.Rule=function(t,e,r){var n={width:t.GetDimen(e),height:t.GetDimen(e),depth:t.GetDimen(e)};"blank"!==r&&(n.mathbackground=t.stack.env.color||"black");var i=t.create("node","mspace",[],n);t.Push(i)},h.rule=function(t,e){var r=t.GetBrackets(e),n=t.GetDimen(e),i=t.GetDimen(e),a=t.create("node","mspace",[],{width:n,height:i,mathbackground:t.stack.env.color||"black"});r&&(a=t.create("node","mpadded",[a],{voffset:r}),r.match(/^\-/)?(o.default.setAttribute(a,"height",r),o.default.setAttribute(a,"depth","+"+r.substr(1))):o.default.setAttribute(a,"height","+"+r)),t.Push(a)},h.MakeBig=function(t,e,r,n){var i=String(n*=1.411764705882353).replace(/(\.\d\d\d).+/,"$1")+"em",o=t.GetDelimiter(e,!0),a=t.create("token","mo",{minsize:i,maxsize:i,fence:!0,stretchy:!0,symmetric:!0},o),s=t.create("node","TeXAtom",[a],{texClass:r});t.Push(s)},h.BuildRel=function(t,e){var r=t.ParseUpTo(e,"\\over"),n=t.ParseArg(e),i=t.create("node","munderover");o.default.setChild(i,0,n),o.default.setChild(i,1,null),o.default.setChild(i,2,r);var a=t.create("node","TeXAtom",[i],{texClass:c.TEXCLASS.REL});t.Push(a)},h.HBox=function(t,e,r,n){t.PushAll(u.default.internalMath(t,t.GetArgument(e),r,n))},h.FBox=function(t,e){var r=u.default.internalMath(t,t.GetArgument(e)),n=t.create("node","menclose",r,{notation:"box"});t.Push(n)},h.Not=function(t,e){t.Push(t.itemFactory.create("not"))},h.Dots=function(t,e){var r=o.default.createEntity("2026"),n=o.default.createEntity("22EF"),i=t.create("token","mo",{stretchy:!1},r),a=t.create("token","mo",{stretchy:!1},n);t.Push(t.itemFactory.create("dots").setProperties({ldots:i,cdots:a}))},h.Matrix=function(t,e,r,n,i,o,s,l,u,c){var p=t.GetNext();if(""===p)throw new a.default("MissingArgFor","Missing argument for %1",t.currentCS);"{"===p?t.i++:(t.string=p+"}"+t.string.slice(t.i+1),t.i=0);var f=t.itemFactory.create("array").setProperty("requireClose",!0);f.arraydef={rowspacing:s||"4pt",columnspacing:o||"1em"},u&&f.setProperty("isCases",!0),c&&(f.setProperty("isNumbered",!0),f.arraydef.side=c),(r||n)&&(f.setProperty("open",r),f.setProperty("close",n)),"D"===l&&(f.arraydef.displaystyle=!0),null!=i&&(f.arraydef.columnalign=i),t.Push(f)},h.Entry=function(t,e){if(t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e})),t.stack.Top().getProperty("isCases")){for(var r=t.string,n=0,i=-1,o=t.i,s=r.length;o<s;){var l=r.charAt(o);if("{"===l)n++,o++;else if("}"===l)0===n?s=0:(0===--n&&i<0&&(i=o-t.i),o++);else{if("&"===l&&0===n)throw new a.default("ExtraAlignTab","Extra alignment tab in \\cases text");"\\"===l?r.substr(o).match(/^((\\cr)[^a-zA-Z]|\\\\)/)?s=0:o+=2:o++}}var c=r.substr(t.i,o-t.i);if(!c.match(/^\s*\\text[^a-zA-Z]/)||i!==c.replace(/\s+$/,"").length-1){var p=u.default.internalMath(t,c,0);t.PushAll(p),t.i=o}}},h.Cr=function(t,e){t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e}))},h.CrLaTeX=function(t,e,r){var o;if(void 0===r&&(r=!1),!r&&"["===t.string.charAt(t.i)){var s=t.GetBrackets(e,""),c=n(u.default.matchDimen(s),2),p=c[0],f=c[1];if(s&&!p)throw new a.default("BracketMustBeDimension","Bracket argument to %1 must be a dimension",t.currentCS);o=p+f}t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e,linebreak:!0}));var h,d=t.stack.Top();if(d instanceof i.ArrayItem){if(o&&d.arraydef.rowspacing){var m=d.arraydef.rowspacing.split(/ /);if(!d.getProperty("rowspacing")){var g=u.default.dimen2em(m[0]);d.setProperty("rowspacing",g)}for(var y=d.getProperty("rowspacing");m.length<d.table.length;)m.push(u.default.Em(y));m[d.table.length-1]=u.default.Em(Math.max(0,y+u.default.dimen2em(o))),d.arraydef.rowspacing=m.join(" ")}}else o&&(h=t.create("node","mspace",[],{depth:o}),t.Push(h)),h=t.create("node","mspace",[],{linebreak:l.TexConstant.LineBreak.NEWLINE}),t.Push(h)},h.HLine=function(t,e,r){null==r&&(r="solid");var n=t.stack.Top();if(!(n instanceof i.ArrayItem)||n.Size())throw new a.default("Misplaced","Misplaced %1",t.currentCS);if(n.table.length){for(var o=n.arraydef.rowlines?n.arraydef.rowlines.split(/ /):[];o.length<n.table.length;)o.push("none");o[n.table.length-1]=r,n.arraydef.rowlines=o.join(" ")}else n.frame.push("top")},h.HFill=function(t,e){var r=t.stack.Top();if(!(r instanceof i.ArrayItem))throw new a.default("UnsupportedHFill","Unsupported use of %1",t.currentCS);r.hfill.push(r.Size())},h.BeginEnd=function(t,e){var r=t.GetArgument(e);if(r.match(/\\/i))throw new a.default("InvalidEnv","Invalid environment name '%1'",r);var n=t.configuration.handlers.get("environment").lookup(r);if(n&&"\\end"===e){if(!n.args[0]){var i=t.itemFactory.create("end").setProperty("name",r);return void t.Push(i)}t.stack.env.closing=r}if(++t.macroCount>t.configuration.options.maxMacros)throw new a.default("MaxMacroSub2","MathJax maximum substitution count exceeded; is there a recursive latex environment?");t.parse("environment",[t,r])},h.Array=function(t,e,r,n,i,o,a,s,l){i||(i=t.GetArgument("\\begin{"+e.getName()+"}"));var u=("c"+i).replace(/[^clr|:]/g,"").replace(/[^|:]([|:])+/g,"$1");i=(i=i.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var c=t.itemFactory.create("array");return c.arraydef={columnalign:i,columnspacing:o||"1em",rowspacing:a||"4pt"},u.match(/[|:]/)&&(u.charAt(0).match(/[|:]/)&&(c.frame.push("left"),c.dashed=":"===u.charAt(0)),u.charAt(u.length-1).match(/[|:]/)&&c.frame.push("right"),u=u.substr(1,u.length-2),c.arraydef.columnlines=u.split("").join(" ").replace(/[^|: ]/g,"none").replace(/\|/g,"solid").replace(/:/g,"dashed")),r&&c.setProperty("open",t.convertDelimiter(r)),n&&c.setProperty("close",t.convertDelimiter(n)),"D"===s?c.arraydef.displaystyle=!0:s&&(c.arraydef.displaystyle=!1),"S"===s&&(c.arraydef.scriptlevel=1),l&&(c.arraydef.useHeight=!1),t.Push(e),c},h.AlignedArray=function(t,e){var r=t.GetBrackets("\\begin{"+e.getName()+"}"),n=h.Array(t,e);return u.default.setArrayAlign(n,r)},h.Equation=function(t,e,r){return t.Push(e),u.default.checkEqnEnv(t),t.itemFactory.create("equation",r).setProperty("name",e.getName())},h.EqnArray=function(t,e,r,n,i,o){t.Push(e),n&&u.default.checkEqnEnv(t),i=(i=i.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var a=t.itemFactory.create("eqnarray",e.getName(),r,n,t.stack.global);return a.arraydef={displaystyle:!0,columnalign:i,columnspacing:o||"1em",rowspacing:"3pt",side:t.options.tagSide,minlabelspacing:t.options.tagIndent},a},h.HandleNoTag=function(t,e){t.tags.notag()},h.HandleLabel=function(t,e){var r=t.GetArgument(e);if(""!==r&&!t.tags.refUpdate){if(t.tags.label)throw new a.default("MultipleCommand","Multiple %1",t.currentCS);if(t.tags.label=r,(t.tags.allLabels[r]||t.tags.labels[r])&&!t.options.ignoreDuplicateLabels)throw new a.default("MultipleLabel","Label '%1' multiply defined",r);t.tags.labels[r]=new p.Label}},h.HandleRef=function(t,e,r){var n=t.GetArgument(e),i=t.tags.allLabels[n]||t.tags.labels[n];i||(t.tags.refUpdate||(t.tags.redo=!0),i=new p.Label);var o=i.tag;r&&(o=t.tags.formatTag(o));var a=t.create("node","mrow",u.default.internalMath(t,o),{href:t.tags.formatUrl(i.id,t.options.baseURL),class:"MathJax_ref"});t.Push(a)},h.Macro=function(t,e,r,n,i){if(n){var o=[];if(null!=i){var s=t.GetBrackets(e);o.push(null==s?i:s)}for(var l=o.length;l<n;l++)o.push(t.GetArgument(e));r=u.default.substituteArgs(t,o,r)}if(t.string=u.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,++t.macroCount>t.configuration.options.maxMacros)throw new a.default("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is there a recursive macro call?")},h.MathChoice=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e),i=t.ParseArg(e),o=t.ParseArg(e);t.Push(t.create("node","MathChoice",[r,n,i,o]))},e.default=h},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var o,a=r(0),s=r(9),l=r(5);!function(t){t.variable=function(t,e){var r=l.default.getFontDef(t),n=t.create("token","mi",r,e);t.Push(n)},t.digit=function(t,e){var r,n=t.configuration.options.digits,i=t.string.slice(t.i-1).match(n),o=l.default.getFontDef(t);i?(r=t.create("token","mn",o,i[0].replace(/[{}]/g,"")),t.i+=i[0].length-1):r=t.create("token","mo",o,e),t.Push(r)},t.controlSequence=function(t,e){var r=t.GetCS();t.parse("macro",[t,r])},t.mathchar0mi=function(t,e){var r=e.attributes||{mathvariant:s.TexConstant.Variant.ITALIC},n=t.create("token","mi",r,e.char);t.Push(n)},t.mathchar0mo=function(t,e){var r=e.attributes||{};r.stretchy=!1;var n=t.create("token","mo",r,e.char);a.default.setProperty(n,"fixStretchy",!0),t.configuration.addNode("fixStretchy",n),t.Push(n)},t.mathchar7=function(t,e){var r=e.attributes||{mathvariant:s.TexConstant.Variant.NORMAL};t.stack.env.font&&(r.mathvariant=t.stack.env.font);var n=t.create("token","mi",r,e.char);t.Push(n)},t.delimiter=function(t,e){var r=e.attributes||{};r=Object.assign({fence:!1,stretchy:!1},r);var n=t.create("token","mo",r,e.char);t.Push(n)},t.environment=function(t,e,r,n){var o=n[0],a=t.itemFactory.create("begin").setProperties({name:e,end:o});a=r.apply(void 0,i([t,a],n.slice(1))),t.Push(a)}}(o||(o={})),e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(3).add({NJcy:"\u040a",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041d",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",NewLine:"\n",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Not:"\u2aec",NotCongruent:"\u2262",NotCupCap:"\u226d",NotEqualTilde:"\u2242\u0338",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",NotLeftTriangleBar:"\u29cf\u0338",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",NotPrecedesEqual:"\u2aaf\u0338",NotReverseElement:"\u220c",NotRightTriangleBar:"\u29d0\u0338",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",Ntilde:"\xd1",Nu:"\u039d",nGg:"\u22d9\u0338",nGt:"\u226b\u20d2",nGtv:"\u226b\u0338",nLl:"\u22d8\u0338",nLt:"\u226a\u20d2",nLtv:"\u226a\u0338",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",neArr:"\u21d7",nearhk:"\u2924",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",ngsim:"\u2275",ngt:"\u226f",ngtr:"\u226f",nhArr:"\u21ce",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",njcy:"\u045a",nlArr:"\u21cd",nlE:"\u2266\u0338",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219a",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nlsim:"\u2274",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nmid:"\u2224",notin:"\u2209",notinE:"\u22f9\u0338",notindot:"\u22f5\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",num:"#",numero:"\u2116",numsp:"\u2007",nvHarr:"\u2904",nvap:"\u224d\u20d2",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwArr:"\u21d6",nwarhk:"\u2923",nwarrow:"\u2196",nwnear:"\u2927"},"n")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PrioritizedList=MathJax._.util.PrioritizedList.PrioritizedList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FunctionList=MathJax._.util.FunctionList.FunctionList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractInputJax=MathJax._.core.InputJax.AbstractInputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFindMath=MathJax._.core.FindMath.AbstractFindMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sortLength=MathJax._.util.string.sortLength,e.quotePattern=MathJax._.util.string.quotePattern,e.unicodeChars=MathJax._.util.string.unicodeChars,e.isPercent=MathJax._.util.string.isPercent,e.split=MathJax._.util.string.split},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.protoItem=MathJax._.core.MathItem.protoItem,e.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,e.STATE=MathJax._.core.MathItem.STATE,e.newState=MathJax._.core.MathItem.newState},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMo=MathJax._.core.MmlTree.MmlNodes.mo.MmlMo},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFactory=MathJax._.core.Tree.Factory.AbstractFactory},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(12),i=r(9),o=r(23),a=r(24),s=r(2);new n.RegExpMap("letter",a.default.variable,/[a-z]/i),new n.RegExpMap("digit",a.default.digit,/[0-9.,]/),new n.RegExpMap("command",a.default.controlSequence,/^\\/),new n.MacroMap("special",{"{":"Open","}":"Close","~":"Tilde","^":"Superscript",_:"Subscript"," ":"Space","\t":"Space","\r":"Space","\n":"Space","'":"Prime","%":"Comment","&":"Entry","#":"Hash","\xa0":"Space","\u2019":"Prime"},o.default),new n.CharacterMap("mathchar0mi",a.default.mathchar0mi,{alpha:"\u03b1",beta:"\u03b2",gamma:"\u03b3",delta:"\u03b4",epsilon:"\u03f5",zeta:"\u03b6",eta:"\u03b7",theta:"\u03b8",iota:"\u03b9",kappa:"\u03ba",lambda:"\u03bb",mu:"\u03bc",nu:"\u03bd",xi:"\u03be",omicron:"\u03bf",pi:"\u03c0",rho:"\u03c1",sigma:"\u03c3",tau:"\u03c4",upsilon:"\u03c5",phi:"\u03d5",chi:"\u03c7",psi:"\u03c8",omega:"\u03c9",varepsilon:"\u03b5",vartheta:"\u03d1",varpi:"\u03d6",varrho:"\u03f1",varsigma:"\u03c2",varphi:"\u03c6",S:["\xa7",{mathvariant:i.TexConstant.Variant.NORMAL}],aleph:["\u2135",{mathvariant:i.TexConstant.Variant.NORMAL}],hbar:["\u210f",{variantForm:!0}],imath:"\u0131",jmath:"\u0237",ell:"\u2113",wp:["\u2118",{mathvariant:i.TexConstant.Variant.NORMAL}],Re:["\u211c",{mathvariant:i.TexConstant.Variant.NORMAL}],Im:["\u2111",{mathvariant:i.TexConstant.Variant.NORMAL}],partial:["\u2202",{mathvariant:i.TexConstant.Variant.ITALIC}],infty:["\u221e",{mathvariant:i.TexConstant.Variant.NORMAL}],prime:["\u2032",{mathvariant:i.TexConstant.Variant.NORMAL,variantForm:!0}],emptyset:["\u2205",{mathvariant:i.TexConstant.Variant.NORMAL}],nabla:["\u2207",{mathvariant:i.TexConstant.Variant.NORMAL}],top:["\u22a4",{mathvariant:i.TexConstant.Variant.NORMAL}],bot:["\u22a5",{mathvariant:i.TexConstant.Variant.NORMAL}],angle:["\u2220",{mathvariant:i.TexConstant.Variant.NORMAL}],triangle:["\u25b3",{mathvariant:i.TexConstant.Variant.NORMAL}],backslash:["\u2216",{mathvariant:i.TexConstant.Variant.NORMAL,variantForm:!0}],forall:["\u2200",{mathvariant:i.TexConstant.Variant.NORMAL}],exists:["\u2203",{mathvariant:i.TexConstant.Variant.NORMAL}],neg:["\xac",{mathvariant:i.TexConstant.Variant.NORMAL}],lnot:["\xac",{mathvariant:i.TexConstant.Variant.NORMAL}],flat:["\u266d",{mathvariant:i.TexConstant.Variant.NORMAL}],natural:["\u266e",{mathvariant:i.TexConstant.Variant.NORMAL}],sharp:["\u266f",{mathvariant:i.TexConstant.Variant.NORMAL}],clubsuit:["\u2663",{mathvariant:i.TexConstant.Variant.NORMAL}],diamondsuit:["\u2662",{mathvariant:i.TexConstant.Variant.NORMAL}],heartsuit:["\u2661",{mathvariant:i.TexConstant.Variant.NORMAL}],spadesuit:["\u2660",{mathvariant:i.TexConstant.Variant.NORMAL}]}),new n.CharacterMap("mathchar0mo",a.default.mathchar0mo,{surd:"\u221a",coprod:["\u2210",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigvee:["\u22c1",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigwedge:["\u22c0",{texClass:s.TEXCLASS.OP,movesupsub:!0}],biguplus:["\u2a04",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigcap:["\u22c2",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigcup:["\u22c3",{texClass:s.TEXCLASS.OP,movesupsub:!0}],int:["\u222b",{texClass:s.TEXCLASS.OP}],intop:["\u222b",{texClass:s.TEXCLASS.OP,movesupsub:!0,movablelimits:!0}],iint:["\u222c",{texClass:s.TEXCLASS.OP}],iiint:["\u222d",{texClass:s.TEXCLASS.OP}],prod:["\u220f",{texClass:s.TEXCLASS.OP,movesupsub:!0}],sum:["\u2211",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigotimes:["\u2a02",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigoplus:["\u2a01",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigodot:["\u2a00",{texClass:s.TEXCLASS.OP,movesupsub:!0}],oint:["\u222e",{texClass:s.TEXCLASS.OP}],bigsqcup:["\u2a06",{texClass:s.TEXCLASS.OP,movesupsub:!0}],smallint:["\u222b",{largeop:!1}],triangleleft:"\u25c3",triangleright:"\u25b9",bigtriangleup:"\u25b3",bigtriangledown:"\u25bd",wedge:"\u2227",land:"\u2227",vee:"\u2228",lor:"\u2228",cap:"\u2229",cup:"\u222a",ddagger:"\u2021",dagger:"\u2020",sqcap:"\u2293",sqcup:"\u2294",uplus:"\u228e",amalg:"\u2a3f",diamond:"\u22c4",bullet:"\u2219",wr:"\u2240",div:"\xf7",odot:["\u2299",{largeop:!1}],oslash:["\u2298",{largeop:!1}],otimes:["\u2297",{largeop:!1}],ominus:["\u2296",{largeop:!1}],oplus:["\u2295",{largeop:!1}],mp:"\u2213",pm:"\xb1",circ:"\u2218",bigcirc:"\u25ef",setminus:"\u2216",cdot:"\u22c5",ast:"\u2217",times:"\xd7",star:"\u22c6",propto:"\u221d",sqsubseteq:"\u2291",sqsupseteq:"\u2292",parallel:"\u2225",mid:"\u2223",dashv:"\u22a3",vdash:"\u22a2",leq:"\u2264",le:"\u2264",geq:"\u2265",ge:"\u2265",lt:"<",gt:">",succ:"\u227b",prec:"\u227a",approx:"\u2248",succeq:"\u2ab0",preceq:"\u2aaf",supset:"\u2283",subset:"\u2282",supseteq:"\u2287",subseteq:"\u2286",in:"\u2208",ni:"\u220b",notin:"\u2209",owns:"\u220b",gg:"\u226b",ll:"\u226a",sim:"\u223c",simeq:"\u2243",perp:"\u22a5",equiv:"\u2261",asymp:"\u224d",smile:"\u2323",frown:"\u2322",ne:"\u2260",neq:"\u2260",cong:"\u2245",doteq:"\u2250",bowtie:"\u22c8",models:"\u22a8",notChar:"\u29f8",Leftrightarrow:"\u21d4",Leftarrow:"\u21d0",Rightarrow:"\u21d2",leftrightarrow:"\u2194",leftarrow:"\u2190",gets:"\u2190",rightarrow:"\u2192",to:["\u2192",{accent:!1}],mapsto:"\u21a6",leftharpoonup:"\u21bc",leftharpoondown:"\u21bd",rightharpoonup:"\u21c0",rightharpoondown:"\u21c1",nearrow:"\u2197",searrow:"\u2198",nwarrow:"\u2196",swarrow:"\u2199",rightleftharpoons:"\u21cc",hookrightarrow:"\u21aa",hookleftarrow:"\u21a9",longleftarrow:"\u27f5",Longleftarrow:"\u27f8",longrightarrow:"\u27f6",Longrightarrow:"\u27f9",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",ldots:"\u2026",cdots:"\u22ef",vdots:"\u22ee",ddots:"\u22f1",dotsc:"\u2026",dotsb:"\u22ef",dotsm:"\u22ef",dotsi:"\u22ef",dotso:"\u2026",ldotp:[".",{texClass:s.TEXCLASS.PUNCT}],cdotp:["\u22c5",{texClass:s.TEXCLASS.PUNCT}],colon:[":",{texClass:s.TEXCLASS.PUNCT}]}),new n.CharacterMap("mathchar7",a.default.mathchar7,{Gamma:"\u0393",Delta:"\u0394",Theta:"\u0398",Lambda:"\u039b",Xi:"\u039e",Pi:"\u03a0",Sigma:"\u03a3",Upsilon:"\u03a5",Phi:"\u03a6",Psi:"\u03a8",Omega:"\u03a9",_:"_","#":"#",$:"$","%":"%","&":"&",And:"&"}),new n.DelimiterMap("delimiter",a.default.delimiter,{"(":"(",")":")","[":"[","]":"]","<":"\u27e8",">":"\u27e9","\\lt":"\u27e8","\\gt":"\u27e9","/":"/","|":["|",{texClass:s.TEXCLASS.ORD}],".":"","\\\\":"\\","\\lmoustache":"\u23b0","\\rmoustache":"\u23b1","\\lgroup":"\u27ee","\\rgroup":"\u27ef","\\arrowvert":"\u23d0","\\Arrowvert":"\u2016","\\bracevert":"\u23aa","\\Vert":["\u2225",{texClass:s.TEXCLASS.ORD}],"\\|":["\u2225",{texClass:s.TEXCLASS.ORD}],"\\vert":["|",{texClass:s.TEXCLASS.ORD}],"\\uparrow":"\u2191","\\downarrow":"\u2193","\\updownarrow":"\u2195","\\Uparrow":"\u21d1","\\Downarrow":"\u21d3","\\Updownarrow":"\u21d5","\\backslash":"\\","\\rangle":"\u27e9","\\langle":"\u27e8","\\rbrace":"}","\\lbrace":"{","\\}":"}","\\{":"{","\\rceil":"\u2309","\\lceil":"\u2308","\\rfloor":"\u230b","\\lfloor":"\u230a","\\lbrack":"[","\\rbrack":"]"}),new n.CommandMap("macros",{displaystyle:["SetStyle","D",!0,0],textstyle:["SetStyle","T",!1,0],scriptstyle:["SetStyle","S",!1,1],scriptscriptstyle:["SetStyle","SS",!1,2],rm:["SetFont",i.TexConstant.Variant.NORMAL],mit:["SetFont",i.TexConstant.Variant.ITALIC],oldstyle:["SetFont",i.TexConstant.Variant.OLDSTYLE],cal:["SetFont",i.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",i.TexConstant.Variant.BOLD],bbFont:["SetFont",i.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",i.TexConstant.Variant.SCRIPT],frak:["SetFont",i.TexConstant.Variant.FRAKTUR],sf:["SetFont",i.TexConstant.Variant.SANSSERIF],tt:["SetFont",i.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],arcsin:["NamedFn"],arccos:["NamedFn"],arctan:["NamedFn"],arg:["NamedFn"],cos:["NamedFn"],cosh:["NamedFn"],cot:["NamedFn"],coth:["NamedFn"],csc:["NamedFn"],deg:["NamedFn"],det:"NamedOp",dim:["NamedFn"],exp:["NamedFn"],gcd:"NamedOp",hom:["NamedFn"],inf:"NamedOp",ker:["NamedFn"],lg:["NamedFn"],lim:"NamedOp",liminf:["NamedOp","lim&thinsp;inf"],limsup:["NamedOp","lim&thinsp;sup"],ln:["NamedFn"],log:["NamedFn"],max:"NamedOp",min:"NamedOp",Pr:"NamedOp",sec:["NamedFn"],sin:["NamedFn"],sinh:["NamedFn"],sup:"NamedOp",tan:["NamedFn"],tanh:["NamedFn"],limits:["Limits",1],nolimits:["Limits",0],overline:["UnderOver","00AF",null,1],underline:["UnderOver","005F"],overbrace:["UnderOver","23DE",1],underbrace:["UnderOver","23DF",1],overparen:["UnderOver","23DC"],underparen:["UnderOver","23DD"],overrightarrow:["UnderOver","2192"],underrightarrow:["UnderOver","2192"],overleftarrow:["UnderOver","2190"],underleftarrow:["UnderOver","2190"],overleftrightarrow:["UnderOver","2194"],underleftrightarrow:["UnderOver","2194"],overset:"Overset",underset:"Underset",stackrel:["Macro","\\mathrel{\\mathop{#2}\\limits^{#1}}",2],over:"Over",overwithdelims:"Over",atop:"Over",atopwithdelims:"Over",above:"Over",abovewithdelims:"Over",brace:["Over","{","}"],brack:["Over","[","]"],choose:["Over","(",")"],frac:"Frac",sqrt:"Sqrt",root:"Root",uproot:["MoveRoot","upRoot"],leftroot:["MoveRoot","leftRoot"],left:"LeftRight",right:"LeftRight",middle:"Middle",llap:"Lap",rlap:"Lap",raise:"RaiseLower",lower:"RaiseLower",moveleft:"MoveLeftRight",moveright:"MoveLeftRight",",":["Spacer",i.TexConstant.Length.THINMATHSPACE],":":["Spacer",i.TexConstant.Length.MEDIUMMATHSPACE],">":["Spacer",i.TexConstant.Length.MEDIUMMATHSPACE],";":["Spacer",i.TexConstant.Length.THICKMATHSPACE],"!":["Spacer",i.TexConstant.Length.NEGATIVETHINMATHSPACE],enspace:["Spacer",".5em"],quad:["Spacer","1em"],qquad:["Spacer","2em"],thinspace:["Spacer",i.TexConstant.Length.THINMATHSPACE],negthinspace:["Spacer",i.TexConstant.Length.NEGATIVETHINMATHSPACE],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],big:["MakeBig",s.TEXCLASS.ORD,.85],Big:["MakeBig",s.TEXCLASS.ORD,1.15],bigg:["MakeBig",s.TEXCLASS.ORD,1.45],Bigg:["MakeBig",s.TEXCLASS.ORD,1.75],bigl:["MakeBig",s.TEXCLASS.OPEN,.85],Bigl:["MakeBig",s.TEXCLASS.OPEN,1.15],biggl:["MakeBig",s.TEXCLASS.OPEN,1.45],Biggl:["MakeBig",s.TEXCLASS.OPEN,1.75],bigr:["MakeBig",s.TEXCLASS.CLOSE,.85],Bigr:["MakeBig",s.TEXCLASS.CLOSE,1.15],biggr:["MakeBig",s.TEXCLASS.CLOSE,1.45],Biggr:["MakeBig",s.TEXCLASS.CLOSE,1.75],bigm:["MakeBig",s.TEXCLASS.REL,.85],Bigm:["MakeBig",s.TEXCLASS.REL,1.15],biggm:["MakeBig",s.TEXCLASS.REL,1.45],Biggm:["MakeBig",s.TEXCLASS.REL,1.75],mathord:["TeXAtom",s.TEXCLASS.ORD],mathop:["TeXAtom",s.TEXCLASS.OP],mathopen:["TeXAtom",s.TEXCLASS.OPEN],mathclose:["TeXAtom",s.TEXCLASS.CLOSE],mathbin:["TeXAtom",s.TEXCLASS.BIN],mathrel:["TeXAtom",s.TEXCLASS.REL],mathpunct:["TeXAtom",s.TEXCLASS.PUNCT],mathinner:["TeXAtom",s.TEXCLASS.INNER],vcenter:["TeXAtom",s.TEXCLASS.VCENTER],buildrel:"BuildRel",hbox:["HBox",0],text:"HBox",mbox:["HBox",0],fbox:"FBox",strut:"Strut",mathstrut:["Macro","\\vphantom{(}"],phantom:"Phantom",vphantom:["Phantom",1,0],hphantom:["Phantom",0,1],smash:"Smash",acute:["Accent","00B4"],grave:["Accent","0060"],ddot:["Accent","00A8"],tilde:["Accent","007E"],bar:["Accent","00AF"],breve:["Accent","02D8"],check:["Accent","02C7"],hat:["Accent","005E"],vec:["Accent","2192"],dot:["Accent","02D9"],widetilde:["Accent","007E",1],widehat:["Accent","005E",1],matrix:"Matrix",array:"Matrix",pmatrix:["Matrix","(",")"],cases:["Matrix","{","","left left",null,".1em",null,!0],eqalign:["Matrix",null,null,"right left",i.TexConstant.Length.THICKMATHSPACE,".5em","D"],displaylines:["Matrix",null,null,"center",null,".5em","D"],cr:"Cr","\\":"CrLaTeX",newline:["CrLaTeX",!0],hline:["HLine","solid"],hdashline:["HLine","dashed"],eqalignno:["Matrix",null,null,"right left",i.TexConstant.Length.THICKMATHSPACE,".5em","D",null,"right"],leqalignno:["Matrix",null,null,"right left",i.TexConstant.Length.THICKMATHSPACE,".5em","D",null,"left"],hfill:"HFill",hfil:"HFill",hfilll:"HFill",bmod:["Macro",'\\mmlToken{mo}[lspace="thickmathspace" rspace="thickmathspace"]{mod}'],pmod:["Macro","\\pod{\\mmlToken{mi}{mod}\\kern 6mu #1}",1],mod:["Macro","\\mathchoice{\\kern18mu}{\\kern12mu}{\\kern12mu}{\\kern12mu}\\mmlToken{mi}{mod}\\,\\,#1",1],pod:["Macro","\\mathchoice{\\kern18mu}{\\kern8mu}{\\kern8mu}{\\kern8mu}(#1)",1],iff:["Macro","\\;\\Longleftrightarrow\\;"],skew:["Macro","{{#2{#3\\mkern#1mu}\\mkern-#1mu}{}}",3],mathcal:["Macro","{\\cal #1}",1],mathscr:["Macro","{\\scr #1}",1],mathrm:["Macro","{\\rm #1}",1],mathbf:["Macro","{\\bf #1}",1],mathbb:["Macro","{\\bbFont #1}",1],Bbb:["Macro","{\\bbFont #1}",1],mathit:["Macro","{\\it #1}",1],mathfrak:["Macro","{\\frak #1}",1],mathsf:["Macro","{\\sf #1}",1],mathtt:["Macro","{\\tt #1}",1],textrm:["HBox",null,i.TexConstant.Variant.NORMAL],textit:["HBox",null,i.TexConstant.Variant.ITALIC],textbf:["HBox",null,i.TexConstant.Variant.BOLD],textsf:["HBox",null,i.TexConstant.Variant.SANSSERIF],texttt:["HBox",null,i.TexConstant.Variant.MONOSPACE],pmb:["Macro","\\rlap{#1}\\kern1px{#1}",1],TeX:["Macro","T\\kern-.14em\\lower.5ex{E}\\kern-.115em X"],LaTeX:["Macro","L\\kern-.325em\\raise.21em{\\scriptstyle{A}}\\kern-.17em\\TeX"]," ":["Macro","\\text{ }"],not:"Not",dots:"Dots",space:"Tilde","\xa0":"Tilde",begin:"BeginEnd",end:"BeginEnd",label:"HandleLabel",ref:"HandleRef",nonumber:"HandleNoTag",mathchoice:"MathChoice",mmlToken:"MmlToken"},o.default),new n.EnvironmentMap("environment",a.default.environment,{array:["AlignedArray"],equation:["Equation",null,!0],"equation*":["Equation",null,!1],eqnarray:["EqnArray",null,!0,!0,"rcl","0 "+i.TexConstant.Length.THICKMATHSPACE,".5em"]},o.default),new n.CharacterMap("not_remap",null,{"\u2190":"\u219a","\u2192":"\u219b","\u2194":"\u21ae","\u21d0":"\u21cd","\u21d2":"\u21cf","\u21d4":"\u21ce","\u2208":"\u2209","\u220b":"\u220c","\u2223":"\u2224","\u2225":"\u2226","\u223c":"\u2241","~":"\u2241","\u2243":"\u2244","\u2245":"\u2247","\u2248":"\u2249","\u224d":"\u226d","=":"\u2260","\u2261":"\u2262","<":"\u226e",">":"\u226f","\u2264":"\u2270","\u2265":"\u2271","\u2272":"\u2274","\u2273":"\u2275","\u2276":"\u2278","\u2277":"\u2279","\u227a":"\u2280","\u227b":"\u2281","\u2282":"\u2284","\u2283":"\u2285","\u2286":"\u2288","\u2287":"\u2289","\u22a2":"\u22ac","\u22a8":"\u22ad","\u22a9":"\u22ae","\u22ab":"\u22af","\u227c":"\u22e0","\u227d":"\u22e1","\u2291":"\u22e2","\u2292":"\u22e3","\u22b2":"\u22ea","\u22b3":"\u22eb","\u22b4":"\u22ec","\u22b5":"\u22ed","\u2203":"\u2204"})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(3).add({Pcy:"\u041f",Poincareplane:"\u210c",Pr:"\u2abb",Prime:"\u2033",Proportion:"\u2237",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",pcy:"\u043f",percnt:"%",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",phmmat:"\u2133",phone:"\u260e",pitchfork:"\u22d4",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",pointint:"\u2a15",pound:"\xa3",pr:"\u227a",prE:"\u2ab3",prcue:"\u227c",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",preceq:"\u2aaf",precsim:"\u227e",primes:"\u2119",prnE:"\u2ab5",prnap:"\u2ab9",prnsim:"\u22e8",prod:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",puncsp:"\u2008"},"p")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(3).add({RBarr:"\u2910",REG:"\xae",Racute:"\u0154",Rang:"\u27eb",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",ReverseElement:"\u220b",ReverseUpEquilibrium:"\u296f",Rho:"\u03a1",RightArrowBar:"\u21e5",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVectorBar:"\u2955",RightTeeVector:"\u295b",RightTriangleBar:"\u29d0",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVectorBar:"\u2954",RightVectorBar:"\u2953",RoundImplies:"\u2970",RuleDelayed:"\u29f4",rAarr:"\u21db",rArr:"\u21d2",rAtail:"\u291c",rBarr:"\u290f",rHar:"\u2964",race:"\u223d\u0331",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",rarrw:"\u219d",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",rbarr:"\u290d",rbbrk:"\u2773",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",reg:"\xae",rfisht:"\u297d",rfloor:"\u230b",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",rightarrow:"\u2192",rightarrowtail:"\u21a3",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightsquigarrow:"\u219d",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",roplus:"\u2a2e",rotimes:"\u2a35",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",rsaquo:"\u203a",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",ruluhar:"\u2968",rx:"\u211e"},"r")},function(t,e,r){"use strict";r.r(e);var n=r(28),i=r(14),o=r(11),a=r(16),s=r(15),l=r(7),u=r(20),c=r(0),p=r(24),f=r(18),h=r(5),d=r(17),m=r(10),g=r(19),y=r(22),v=r(12),b=r(6),T=r(9),P=r(1),S=r(4),x=r(21),A=r(13),M=r(23);Object(n.combineWithMathJax)({_:{input:{tex_ts:i,tex:{Configuration:o,FilterUtil:a,FindTeX:s,MapHandler:l,NodeFactory:u,NodeUtil:c,ParseMethods:p,ParseOptions:f,ParseUtil:h,Stack:d,StackItem:m,StackItemFactory:g,Symbol:y,SymbolMap:v,Tags:b,TexConstants:T,TexError:P,TexParser:S,base:{BaseConfiguration:x,BaseItems:A,BaseMethods:M}}}}});var O=r(8);!function(t){if(MathJax.startup){MathJax.startup.registerConstructor("tex",i.TeX),MathJax.startup.useInput("tex"),MathJax.config.tex||(MathJax.config.tex={});var e=MathJax.config.tex.packages;MathJax.config.tex.packages=t,e&&Object(O.insert)(MathJax.config.tex,{packages:e})}}(["base"])}]);