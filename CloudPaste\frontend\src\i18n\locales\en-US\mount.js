export default {
  mount: {
    title: "Mount Explorer",
    permissionRequired: "Admin privileges or a valid API key are required to access mount explorer, please",
    loginAuth: "login to admin panel or configure API key",
    cancel: "Cancel",
    backToFileList: "Back to File List",
    noPermissionForPath: "You don't have permission to access this directory. You can only access {path} and its subdirectories.",
    batchDelete: {
      title: "Confirm Batch Delete",
      message: "Are you sure you want to delete the selected {count} items? This action cannot be undone.",
      selectedItems: "Selected items:",
      folder: "(Folder)",
      moreItems: "... and {count} more items",
      confirmButton: "Delete",
      cancelButton: "Cancel",
    },
    operations: {
      upload: "Upload",
      createFolder: "New Folder",
      tasks: "Tasks",
      refresh: "Refresh",
    },
    createFolder: {
      enterName: "Please enter folder name",
      folderName: "Folder Name",
      create: "Create",
      cancel: "Cancel",
    },
    viewModes: {
      list: "List View",
      grid: "Grid View",
      gallery: "Gallery View",
    },
    fileList: {
      loading: "Loading...",
      empty: "This directory is empty",
      noMountPoints: "No available mount points",
      name: "Name",
      size: "Size",
      modifiedTime: "Modified Time",
      type: "Type",
      actions: "Actions",
      selectAll: "Select All",
      deselectAll: "Deselect All",
      clickToSort: "Click to sort",
    },
    rename: {
      title: "Rename",
      enterNewName: "Please enter new name",
      newName: "New Name",
      cancel: "Cancel",
      confirm: "Confirm",
    },
    delete: {
      title: "Confirm Delete",
      message: "Are you sure you want to delete {type} {name}?",
      folderWarning: "This operation will delete the folder and all its contents.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    messages: {
      apiKeyInfoUpdated: "API key information updated",
      refreshSuccess: "Refresh successful",
      refreshFailed: "Refresh failed, please try again",
      getDirectoryContentFailed: "Failed to get directory content: {message}",
      getDirectoryContentFailedUnknown: "Failed to get directory content: {message}",
      fileUploadSuccess: "File upload successful",
      fileUploadFailed: "File upload failed: {message}",
      fileUploadFailedUnknown: "File upload failed: {message}",
      uploadCancelling: "Cancelling upload...",
      folderCreateSuccess: "Folder created successfully",
      folderCreateFailed: "Folder creation failed: {message}",
      folderCreateFailedUnknown: "Folder creation failed: {message}",
      renameSuccess: "{type} renamed successfully",
      renameFailed: "Rename failed: {message}",
      renameFailedUnknown: "Rename failed: {message}",
      deleteSuccess: "{type} deleted successfully",
      deleteFailed: "Delete failed: {message}",
      deleteFailedUnknown: "Delete failed: {message}",
      downloadPreparing: "Preparing file download...",
      downloadSuccess: "File download successful",
      downloadStarted: "Started downloading {name}",
      downloadFailed: "File download failed: {message}",
      downloadFailedUnknown: "File download failed: {message}",
      uploading: "Uploading {name}...",
      initializationFailed: "Initialization failed: {message}",
      batchDeleteFailed: "Batch delete failed: {message}",
      copySuccess: "{message}",
      copyFailed: "Copy failed: {message}",
      getFileLinkFailed: "Failed to get file direct link: {message}",
      getFileLinkError: "Error getting file direct link: {message}",
      linkCopied: "File direct link copied to clipboard",
      noItemsSelected: "Please select items to operate on",
      previewError: "File preview loading failed",
      uploadSuccess: "Upload successful",
      uploadFailed: "Upload failed: {message}",
    },
    filePreview: {
      downloadFile: "Download File",
      directPreview: "Direct Preview",
      generating: "Generating...",
      fileSize: "File Size:",
      modifiedTime: "Modified Time:",
      fileType: "File Type:",
      unknown: "Unknown",
      editMode: "Edit",
      previewMode: "Preview",
      saving: "Saving...",
      save: "Save",
      cancel: "Cancel",
      language: "Language:",
      autoDetect: "Auto Detect",
      configFile: "Config File",
      loadingPreview: "Loading Office preview...",
      previewError: "Failed to load file preview",
      retryLoad: "Please try to reload or download file to view",
      retry: "Retry",
      cannotPreview: "File cannot be previewed",
      downloadToView: "Current file type does not support online preview, please click download button to download and view",
      wordPreview: "Word Document Preview",
      excelPreview: "Excel Spreadsheet Preview",
      powerpointPreview: "PowerPoint Presentation Preview",
      exitFullscreen: "Exit Fullscreen",
      fullscreen: "Fullscreen",
      useMicrosoftPreview: "Use Microsoft Preview",
      useGooglePreview: "Use Google Preview",
      htmlPreview: "HTML Preview",
      browserNotSupport: "Your browser does not support",
      videoTag: "video tag",
      audioTag: "audio tag",
      cannotLoadText: "Cannot load text content",
      loadTextError: "Error loading text content",
      getS3LinkFailed: "Failed to get S3 direct link: {message}",
      s3PreviewFailed: "S3 direct preview failed: {message}",
      fileTooLarge: "File content is too large, exceeds maximum limit (10MB). Please reduce file size and try again.",
      saveFileFailed: "Failed to save file: {message}",
      saveFileError: "Error occurred while saving file: {message}",
      fileContentTooLarge: "File content is too large, exceeds server limit",
      noPermissionUpdate: "No permission to update this file",
      serverNoResponse: "Server not responding, please check network connection",
      fileCreated: "File created successfully",
      fileUpdated: "File updated successfully",
    },
    uploadModal: {
      title: "Upload Files",
      uploadMethod: "Upload Method:",
      presignedUpload: "Presigned Upload",
      recommended: "Recommended",
      directUpload: "Direct Upload",
      multipartUpload: "Multipart Upload",
      directMode: "Direct Mode",
      presignedMode: "Presigned Mode",
      multipartMode: "Multipart Mode",
      directModeDesc: "Upload directly through server, no progress bar, suitable for small files",
      presignedModeDesc: "Upload directly to storage server, avoid Worker CPU limits, faster speed",
      multipartModeDesc: "Upload through server in chunks, suitable for large files or unstable networks",
      dragDropHere: "Drop files here",
      clickOrDragToUpload: "Click or drag files here to upload",
      multiFileSupport: "Multiple file upload supported",
      pasteSupport: "Ctrl+V paste file support",
      selectedFiles: "Selected {count} files",
      clearAll: "Clear All",
      totalProgress: "Total Progress",
      uploadSpeed: "Upload Speed:",
      uploading: "Uploading {current}/{total}: {fileName}",
      cancel: "Cancel",
      cancelUpload: "Cancel Upload",
      startUpload: "Start Upload",
      fileStatus: {
        pending: "Pending",
        uploading: "{progress}%",
        success: "Upload Success",
        error: "Upload Failed",
      },
      cancelSingleUpload: "Cancel Upload",
      retryUpload: "Retry",
      removeFile: "Remove",
      pasteFileAdded: "File added from clipboard",
      confirmCancelUpload: "Files are uploading, are you sure you want to cancel and close?",
      confirmClearFiles: "Are you sure you want to clear all files?",
      noFilesSelected: "Please select files to upload first",
      uploadStarted: "Started uploading files...",
      allFilesUploaded: "All files uploaded successfully!",
      someFilesFailed: "Some files failed to upload, please check error messages",
      uploadCancelled: "Upload cancelled",
      noFilesToUpload: "No files to upload",
      allFilesUploadFailed: "All files failed to upload",
      checkFilesValid: ", please check if files are valid",
      partialUploadSuccess: "Uploaded {success} files, {failed} files failed",
      allFilesUploadSuccess: "Successfully uploaded {count} files",
      cancelUploadError: "Failed to cancel upload: {message}",
      allUploadsCancelled: "Cancelled all file uploads",
      retryUploadSuccess: "File {fileName} retry upload successful",
      retryUploadFailed: "File {fileName} retry upload failed: {message}",
      retryUploadError: "File {fileName} retry upload error: {message}",
      uploadFailed: "Upload failed",
      uploadError: "Error occurred during upload",
      unknownError: "Unknown error",
    },
    copyModal: {
      title: "Select Target Folder",
      selectedInfo: "Selected: {count} items ({folders} folders, {files} files)",
      targetLocation: "Target Location:",
      loading: "Loading...",
      rootDirectory: "Root Directory",
      cancel: "Cancel",
      confirmCopy: "Confirm Copy",
      copying: "Copying...",
      warnings: {
        recursiveCopy: "Warning: Cannot copy folder to itself or its subdirectories, this may cause infinite recursion.",
        selfCopy: "Warning: Cannot copy folder to itself.",
      },
      confirmPotentialIssue: "Potential issue detected: {warning}\n\nDo you still want to continue copying?",
    },
    fileItem: {
      download: "Download",
      getLink: "Get Direct Link",
      rename: "Rename",
      delete: "Delete",
      preview: "Preview",
      copy: "Copy",
      move: "Move",
      properties: "Properties",
    },
    audioPreview: {
      loadingAudio: "Loading audio...",
      audioPlayer: "Audio Player",
    },
    videoPreview: {
      loadingVideo: "Loading video...",
      videoPlayer: "Video Player",
    },

    taskManager: {
      title: "Task Manager",
      noTasks: "No tasks",
      noTasksDescription: "No active or completed tasks currently",
      activeTasks: "Active tasks ({count})",
      completedTasks: "Completed tasks ({count})",
      clearCompleted: "Clear Completed",
      cancel: "Cancel",
      retry: "Retry",
      details: "Details",
      hideDetails: "Hide Details",
      status: {
        pending: "Pending",
        running: "Running",
        completed: "Completed",
        failed: "Failed",
        cancelled: "Cancelled",
      },
      types: {
        copy: "Copy",
        upload: "Upload",
        delete: "Delete",
        download: "Download",
      },
      progress: "Progress: {current}/{total}",
      timeElapsed: "Time elapsed: {time}",
      createdAt: "Created at: {time}",
      updatedAt: "Updated at: {time}",
      error: "Error: {message}",
      confirmCancel: "Are you sure you want to cancel this task?",
      confirmClearCompleted: "Are you sure you want to clear all completed tasks?",
      downloading: "Downloading",
      uploading: "Uploading",
      processing: "Processing",
      currentFile: "Current file: {fileName}",
      processed: "Processed: {current}/{total}",
      completedAt: "Completed at: {time}",
      processedItems: "Processed items: {current}/{total}",
      success: "Success: {count}",
      skipped: "Skipped: {count}",
      failed: "Failed: {count}",
      partialComplete: "Partial Complete",
      copyTask: "Copy Task",
      uploadTask: "Upload Task",
      deleteTask: "Delete Task",
      downloadTask: "Download Task",
      unknownTask: "Unknown Task",
      waiting: "Waiting",
      unknown: "Unknown",
      unknownTime: "Unknown time",
      copyTaskName: "Copy {count} items to {path}",
      copyStarted: "Started copying {count} items to {path}, check progress in task manager",
      // File status
      fileStatus: {
        pending: "Pending",
        downloading: "Downloading",
        completed: "Completed",
        failed: "Failed",
        preparing: "Preparing...",
      },
      // File progress
      fileProgress: "File Progress",
      filesCount: "files",
    },
    linkCopied: "File direct link copied to clipboard",
    fileTypes: {
      folder: "Folder",
      file: "File",
      image: "Image",
      video: "Video",
      audio: "Audio",
      document: "Document",
      archive: "Archive",
      code: "Code",
      unknown: "Unknown Type",
    },
    sizeUnits: {
      bytes: "Bytes",
      kb: "KB",
      mb: "MB",
      gb: "GB",
      tb: "TB",
    },
  },
  breadcrumb: {
    navigation: "Breadcrumb Navigation",
    root: "Root Directory",
    batchOperations: "Batch Operations",
    enableSelection: "Enable Selection",
    exitSelection: "Exit Selection",
    copySelected: "Copy Selected",
    deleteSelected: "Delete Selected",
    selectedCount: "({count})",
    // Mobile short text
    mobile: {
      enableSelection: "Select",
      exitSelection: "Exit",
      copySelected: "Copy",
      deleteSelected: "Delete",
    },
  },
};
