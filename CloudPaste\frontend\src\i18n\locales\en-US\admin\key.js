export default {
  "keyManagement": {
    "title": "API Key Management",
    "refresh": "Refresh",
    "bulkDelete": "Bulk Delete",
    "delete": "Delete",
    "create": "Create New Key",
    "createShort": "Create",
    "lastRefreshed": "Last Refreshed",
    "table": {
      "select": "Select",
      "name": "Name",
      "key": "Key",
      "permissions": "Permissions",
      "basicPath": "Basic Path",
      "expires": "Expires",
      "lastUsed": "Last Used",
      "actions": "Actions",
      "noData": "No API keys available",
      "loading": "Loading..."
    },
    "keyName": "Name",
    "key": "Key",
    "permissions": {
      "text": "Text",
      "file": "File",
      "mount": "Mount",
      "readOnly": "Read Only",
      "none": "None"
    },
    "permissionsColumn": "Permissions",
    "basicPath": "Basic Path",
    "createdAt": "Created At",
    "expiresAt": "Expires At",
    "lastUsed": "Last Used",
    "actions": "Actions",
    "loading": "Loading...",
    "loadingKeys": "Loading keys...",
    "neverUsed": "Never Used",
    "noKeysTitle": "No API Keys",
    "noKeysDescription": "You haven't created any API keys yet. Click the button above to create your first key.",
    "textPermissionFull": "Text",
    "filePermissionFull": "File",
    "mountPermissionFull": "Mount",
    "noPermission": "No Permission",
    "copyKey": "Copy",
    "copyKeyFull": "Copy Full Key",
    "edit": "Edit",
    "deleteKey": "Delete",
    "neverExpires": "Never Expires",
    "createModal": {
      "title": "Create New API Key",
      "tabs": {
        "basic": "Basic Information",
        "path": "Path Selection"
      },
      "keyName": "Key Name",
      "keyNamePlaceholder": "Enter key name",
      "keyNameHelp": "Set an easily recognizable name for your API key",
      "useCustomKey": "Use Custom Key",
      "customKey": "Custom Key",
      "customKeyPlaceholder": "Enter custom key (optional)",
      "customKeyHelp": "Only letters, numbers, underscores and hyphens allowed",
      "expiration": "Expiration",
      "expirationOptions": {
        "1d": "1 Day",
        "7d": "7 Days",
        "30d": "30 Days",
        "never": "Never Expires",
        "custom": "Custom"
      },
      "customExpiration": "Custom Expiration Time",
      "customExpirationPlaceholder": "Enter number of days",
      "permissions": {
        "text": "Text Permission",
        "file": "File Permission",
        "mount": "Mount Permission"
      },
      "textPermission": "Text Permission",
      "filePermission": "File Permission",
      "mountPermission": "Mount Permission",
      "readOnlyMount": "Read-only Mount",
      "basicPath": "Basic Path",
      "basicPathPlaceholder": "/",
      "basicPathHelp": "Set the basic path that the API key can access, defaults to root path",
      "selectPath": "Select Path",
      "securityTip": "Security Tip",
      "securityMessage": "Please keep your API key safe and do not use it in public places or insecure environments.",
      "pathSelector": {
        "title": "Select Basic Path",
        "rootDirectory": "Root Directory",
        "selectDirectory": "Select Directory",
        "currentPath": "Current Selection",
        "confirm": "Confirm Path",
        "cancel": "Cancel",
        "loading": "Loading...",
        "loadError": "Load Failed",
        "noDirectories": "No subdirectories in this directory"
      },
      "create": "Create",
      "creating": "Creating...",
      "processing": "Creating...",
      "cancel": "Cancel",
      "errors": {
        "nameRequired": "Key name cannot be empty",
        "customKeyRequired": "Custom key cannot be empty",
        "customKeyFormat": "Custom key format is incorrect, only letters, numbers, underscores and hyphens allowed",
        "expirationRequired": "Custom expiration time cannot be empty",
        "invalidExpiration": "Invalid expiration time",
        "createFailed": "Failed to create key"
      }
    },
    "editModal": {
      "title": "Edit API Key",
      "tabs": {
        "basic": "Basic Information",
        "path": "Path Selection"
      },
      "keyName": "Key Name",
      "keyNamePlaceholder": "Enter key name",
      "keyNameHelp": "Set an easily recognizable name for your API key",
      "expiration": "Expiration",
      "expirationOptions": {
        "1d": "1 Day",
        "7d": "7 Days",
        "30d": "30 Days",
        "never": "Never Expires",
        "custom": "Custom"
      },
      "customExpiration": "Custom Expiration Time",
      "customExpirationPlaceholder": "Enter number of days",
      "permissions": {
        "text": "Text Permission",
        "file": "File Permission",
        "mount": "Mount Permission"
      },
      "textPermission": "Text Permission",
      "filePermission": "File Permission",
      "mountPermission": "Mount Permission",
      "basicPath": "Basic Path",
      "basicPathPlaceholder": "/",
      "basicPathHelp": "Set the basic path that the API key can access, defaults to root path",
      "selectPath": "Select Path",
      "securityTip": "Security Tip",
      "securityMessage": "Please keep your API key safe and do not use it in public places or insecure environments.",
      "pathSelector": {
        "title": "Select Basic Path",
        "rootDirectory": "Root Directory",
        "selectDirectory": "Select Directory",
        "currentPath": "Current Selection",
        "confirm": "Confirm Path",
        "cancel": "Cancel",
        "loading": "Loading...",
        "loadError": "Load Failed",
        "noDirectories": "No subdirectories in this directory"
      },
      "update": "Update",
      "updating": "Updating...",
      "processing": "Updating...",
      "cancel": "Cancel",
      "errors": {
        "nameRequired": "Key name cannot be empty",
        "expirationRequired": "Custom expiration time cannot be empty",
        "invalidExpiration": "Invalid expiration time",
        "updateFailed": "Failed to update key"
      }
    },
    "success": {
      "created": "API key created successfully",
      "createdAndCopied": "Key created and copied to clipboard",
      "updated": "API key updated successfully",
      "deleted": "API key deleted successfully",
      "bulkDeleted": "Bulk delete successful, deleted {count} keys",
      "copied": "Key copied to clipboard"
    },
    "error": {
      "cannotLoadList": "Cannot load key list",
      "loadFailed": "Failed to load API keys",
      "copyFailed": "Failed to copy to clipboard",
      "deleteFailed": "Failed to delete key",
      "bulkDeleteFailed": "Bulk delete failed",
      "noKeysSelected": "Please select keys to delete"
    },
    "confirmDelete": "Are you sure you want to delete key \"{name}\"? This action cannot be undone.",
    "confirmBulkDelete": "Are you sure you want to delete the selected {count} keys? This action cannot be undone.",
    "selectKeysFirst": "Please select keys to delete first",
    "bulkDeleteConfirm": "Are you sure you want to delete the selected {count} keys? This action cannot be undone."
  }
};