{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "license": "Apache-2.0", "scripts": {"dev": "vite", "build": "vite build", "vercel-build": "node vercel-build.cjs && vite build", "preview": "vite preview", "clean": "node clean-deps.js"}, "dependencies": {"@imengyu/vue3-context-menu": "^1.5.1", "@yeger/vue-masonry-wall": "^5.0.21", "aplayer": "^1.10.1", "artplayer": "^5.2.3", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "chart.js": "^4.4.8", "docx": "^9.3.0", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "jszip": "^3.10.1", "mime-db": "^1.54.0", "photoswipe": "^5.4.4", "pinia": "^3.0.3", "postcss": "^8.4.35", "qrcode": "^1.5.3", "tailwindcss": "^3.4.1", "vditor": "^3.11.0", "vite-plugin-pwa": "^1.0.0", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-i18n": "^9.14.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "terser": "^5.39.0", "vite": "^4.5.0"}}