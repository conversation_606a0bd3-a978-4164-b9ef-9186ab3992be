export default {
  mount: {
    title: "挂载浏览",
    permissionRequired: "需要管理员权限或有效的API密钥才能访问挂载浏览，请",
    loginAuth: "登录管理面板或配置API密钥",
    cancel: "取消",
    backToFileList: "返回文件列表",
    noPermissionForPath: "您没有权限访问此目录的内容。您只能访问 {path} 及其子目录。",
    batchDelete: {
      title: "确认批量删除",
      message: "您确定要删除选中的 {count} 个项目吗？此操作不可撤销。",
      selectedItems: "选中的项目:",
      folder: "(文件夹)",
      moreItems: "... 等 {count} 个项目",
      confirmButton: "删除",
      cancelButton: "取消",
    },
    operations: {
      upload: "上传",
      createFolder: "新建文件夹",
      refresh: "刷新",
      viewMode: "视图模式",
      batchOperations: "批量操作",
      tasks: "任务管理",
    },
    createFolder: {
      enterName: "请输入文件夹名称",
      folderName: "文件夹名称",
      placeholder: "新文件夹",
      cancel: "取消",
      create: "创建",
    },
    viewModes: {
      list: "列表视图",
      grid: "网格视图",
      gallery: "图廊视图",
    },
    fileList: {
      loading: "加载中...",
      empty: "此目录为空",
      noMountPoints: "无可用挂载点",
      name: "名称",
      size: "大小",
      modifiedTime: "修改时间",
      type: "类型",
      actions: "操作",
      selectAll: "全选",
      deselectAll: "取消全选",
      clickToSort: "点击排序",
    },
    rename: {
      title: "重命名",
      enterNewName: "请输入新的名称",
      newName: "新名称",
      cancel: "取消",
      confirm: "确认",
    },
    delete: {
      title: "确认删除",
      message: "您确定要删除{type} {name} 吗？",
      folderWarning: "此操作将删除文件夹及其所有内容。",
      cancel: "取消",
      confirm: "删除",
    },
    messages: {
      apiKeyInfoUpdated: "已更新API密钥信息",
      refreshSuccess: "刷新成功",
      refreshFailed: "刷新失败，请重试",
      getDirectoryContentFailed: "获取目录内容失败: {message}",
      getDirectoryContentFailedUnknown: "获取目录内容失败: {message}",
      fileUploadSuccess: "文件上传成功",
      fileUploadFailed: "文件上传失败: {message}",
      fileUploadFailedUnknown: "文件上传失败: {message}",
      uploadCancelling: "正在取消上传...",
      folderCreateSuccess: "文件夹创建成功",
      folderCreateFailed: "文件夹创建失败: {message}",
      folderCreateFailedUnknown: "文件夹创建失败: {message}",
      renameSuccess: "{type}重命名成功",
      renameFailed: "重命名失败: {message}",
      renameFailedUnknown: "重命名失败: {message}",
      deleteSuccess: "{type}删除成功",
      deleteFailed: "删除失败: {message}",
      deleteFailedUnknown: "删除失败: {message}",
      downloadPreparing: "准备下载文件...",
      downloadSuccess: "文件下载成功",
      downloadStarted: "开始下载 {name}",
      downloadFailed: "文件下载失败: {message}",
      downloadFailedUnknown: "文件下载失败: {message}",
      uploading: "正在上传 {name}...",
      initializationFailed: "初始化失败: {message}",
      batchDeleteFailed: "批量删除失败: {message}",
      previewLoadFailed: "加载文件预览失败: {message}",
      previewLoadFailedUnknown: "加载文件预览失败: {message}",
      previewError: "文件预览加载失败",
      uploadError: "上传失败: {message}",
      uploadErrorUnknown: "上传失败: {message}",
      batchDeleteInProgress: "正在删除选中的项目...",
      batchDeletePartialSuccess: "删除操作部分成功，{success} 个成功，{failed} 个失败",
      batchDeleteSuccess: "成功删除了 {count} 个项目",
      batchDeleteFailedUnknown: "批量删除失败: {message}",
      copySuccess: "{message}",
      copyFailed: "复制失败: {message}",
      getFileLinkFailed: "获取文件直链失败: {message}",
      getFileLinkError: "获取文件直链错误: {message}",
      noItemsSelected: "请选择要操作的项目",
      confirmBatchDelete: "确定要删除选中的 {count} 个项目吗？此操作不可撤销。",
      uploadSuccess: "上传成功",
      uploadFailed: "上传失败: {message}",
    },
    filePreview: {
      downloadFile: "下载文件",
      directPreview: "直链预览",
      generating: "生成中...",
      fileSize: "文件大小:",
      modifiedTime: "修改时间:",
      fileType: "文件类型:",
      unknown: "未知",
      editMode: "编辑模式",
      previewMode: "预览模式",
      saving: "保存中...",
      save: "保存",
      cancel: "取消",
      language: "语言:",
      autoDetect: "自动检测",
      configFile: "配置文件",
      loadingPreview: "正在加载Office预览...",
      previewError: "加载文件预览失败",
      retryLoad: "请尝试重新加载或下载文件查看",
      retry: "重试",
      cannotPreview: "文件无法预览",
      downloadToView: "当前文件类型不支持在线预览，请点击下载按钮下载查看",
      wordPreview: "Word文档预览",
      excelPreview: "Excel表格预览",
      powerpointPreview: "PowerPoint演示文稿预览",
      exitFullscreen: "退出全屏",
      fullscreen: "全屏",
      useMicrosoftPreview: "使用Microsoft预览",
      useGooglePreview: "使用Google预览",
      htmlPreview: "HTML预览",
      browserNotSupport: "您的浏览器不支持",
      videoTag: "视频标签",
      audioTag: "音频标签",
      cannotLoadText: "无法加载文本内容",
      loadTextError: "加载文本内容时出错",
      getS3LinkFailed: "获取S3直链失败: {message}",
      s3PreviewFailed: "S3直链预览失败: {message}",
      fileTooLarge: "文件内容过大，超过最大限制(10MB)。请减少文件大小后重试。",
      saveFileFailed: "保存文件失败: {message}",
      saveFileError: "保存文件时发生错误: {message}",
      fileContentTooLarge: "文件内容过大，超过服务器限制",
      noPermissionUpdate: "没有权限更新该文件",
      serverNoResponse: "服务器无响应，请检查网络连接",
      fileCreated: "文件创建成功",
      fileUpdated: "文件更新成功",
    },
    audioPreview: {
      loadingAudio: "正在加载音频...",
      audioPlayer: "音频播放器",
    },
    videoPreview: {
      loadingVideo: "正在加载视频...",
      videoPlayer: "视频播放器",
    },

    uploadModal: {
      title: "上传文件",
      uploadMethod: "上传方式:",
      presignedUpload: "预签名直传",
      recommended: "推荐",
      directUpload: "直接上传",
      multipartUpload: "分片上传",
      directMode: "直接模式",
      presignedMode: "直传模式",
      multipartMode: "分片模式",
      directModeDesc: "通过服务器直接上传，不显示进度条，适合小文件",
      presignedModeDesc: "直接上传到存储服务器，避免Worker CPU限制，速度更快",
      multipartModeDesc: "通过服务器分片上传，适合大文件或不稳定网络环境",
      dragDropHere: "拖放文件到这里",
      clickOrDragToUpload: "点击或拖动文件到这里上传",
      multiFileSupport: "支持多文件上传",
      pasteSupport: "支持 Ctrl+V 粘贴文件",
      selectedFiles: "已选择 {count} 个文件",
      clearAll: "清除全部",
      totalProgress: "总进度",
      uploadSpeed: "上传速度:",
      uploading: "正在上传 {current}/{total}: {fileName}",
      cancel: "取消",
      cancelUpload: "取消上传",
      startUpload: "开始上传",
      fileStatus: {
        pending: "待上传",
        uploading: "{progress}%",
        success: "上传成功",
        error: "上传失败",
      },
      cancelSingleUpload: "取消上传",
      retryUpload: "重试",
      removeFile: "移除",
      pasteFileAdded: "已从剪贴板添加文件",
      confirmCancelUpload: "正在上传文件，确定要取消并关闭吗？",
      confirmClearFiles: "确定要清除所有文件吗？",
      noFilesSelected: "请先选择要上传的文件",
      uploadStarted: "开始上传文件...",
      allFilesUploaded: "所有文件上传完成！",
      someFilesFailed: "部分文件上传失败，请检查错误信息",
      uploadCancelled: "上传已取消",
      noFilesToUpload: "没有可上传的文件",
      allFilesUploadFailed: "所有文件上传失败",
      checkFilesValid: "，请检查文件是否有效",
      partialUploadSuccess: "已上传 {success} 个文件，{failed} 个文件失败",
      allFilesUploadSuccess: "已成功上传 {count} 个文件",
      cancelUploadError: "取消上传失败: {message}",
      allUploadsCancelled: "已取消所有文件的上传",
      retryUploadSuccess: "文件 {fileName} 重新上传成功",
      retryUploadFailed: "文件 {fileName} 重新上传失败: {message}",
      retryUploadError: "文件 {fileName} 重新上传错误: {message}",
      uploadFailed: "上传失败",
      uploadError: "上传过程中发生错误",
      unknownError: "未知错误",
    },
    taskManager: {
      title: "任务管理",
      noTasks: "暂无任务",
      noTasksDescription: "当前没有正在进行或已完成的任务",
      activeTasks: "正在进行的任务 ({count})",
      completedTasks: "已完成的任务 ({count})",
      clearCompleted: "清除已完成",
      cancel: "取消",
      retry: "重试",
      details: "详情",
      hideDetails: "隐藏详情",
      status: {
        pending: "待处理",
        running: "运行中",
        completed: "已完成",
        failed: "失败",
        cancelled: "已取消",
      },
      types: {
        copy: "复制",
        upload: "上传",
        delete: "删除",
        download: "下载",
      },
      progress: "进度: {current}/{total}",
      timeElapsed: "耗时: {time}",
      createdAt: "创建时间: {time}",
      updatedAt: "更新时间: {time}",
      error: "错误: {message}",
      confirmCancel: "确定要取消这个任务吗？",
      confirmClearCompleted: "确定要清除所有已完成的任务吗？",
      downloading: "下载中",
      uploading: "上传中",
      processing: "处理中",
      currentFile: "当前文件: {fileName}",
      processed: "已处理: {current}/{total}",
      completedAt: "完成时间: {time}",
      processedItems: "处理项目: {current}/{total}",
      success: "成功: {count}",
      skipped: "跳过: {count}",
      failed: "失败: {count}",
      partialComplete: "部分完成",
      copyTask: "复制任务",
      uploadTask: "上传任务",
      deleteTask: "删除任务",
      downloadTask: "下载任务",
      unknownTask: "未知任务",
      waiting: "等待中",
      unknown: "未知",
      unknownTime: "未知时间",
      copyTaskName: "复制 {count} 个项目到 {path}",
      copyStarted: "开始复制 {count} 个项目到 {path}，可在任务管理中查看进度",
      // 文件状态
      fileStatus: {
        pending: "等待",
        downloading: "下载中",
        completed: "已完成",
        failed: "失败",
        preparing: "准备中...",
      },
      // 文件进度
      fileProgress: "文件进度",
      filesCount: "个文件",
    },
    copyModal: {
      title: "选择目标文件夹",
      selectedInfo: "已选择: {count} 个项目 ({folders} 个文件夹, {files} 个文件)",
      targetLocation: "目标位置:",
      loading: "加载中...",
      rootDirectory: "根目录",
      cancel: "取消",
      confirmCopy: "确认复制",
      copying: "复制中...",
      warnings: {
        recursiveCopy: "警告：不能将文件夹复制到其自身或其子目录中，这可能导致无限递归。",
        selfCopy: "警告：不能将文件夹复制到其自身。",
      },
      confirmPotentialIssue: "检测到潜在问题：{warning}\n\n是否仍要继续复制？",
    },
    linkCopied: "文件直链已复制到剪贴板",
    fileItem: {
      download: "下载",
      getLink: "获取直链",
      rename: "重命名",
      delete: "删除",
      preview: "预览",
      copy: "复制",
      move: "移动",
      properties: "属性",
    },
    fileTypes: {
      folder: "文件夹",
      file: "文件",
      image: "图片",
      video: "视频",
      audio: "音频",
      document: "文档",
      archive: "压缩包",
      code: "代码",
      unknown: "未知类型",
    },
    sizeUnits: {
      bytes: "字节",
      kb: "KB",
      mb: "MB",
      gb: "GB",
      tb: "TB",
    },
  },
  breadcrumb: {
    navigation: "面包屑导航",
    root: "根目录",
    batchOperations: "批量操作",
    enableSelection: "启用勾选",
    exitSelection: "退出勾选",
    copySelected: "复制选中",
    deleteSelected: "删除选中",
    selectedCount: "({count})",
    // 移动端短文本
    mobile: {
      enableSelection: "勾选",
      exitSelection: "退出",
      copySelected: "复制",
      deleteSelected: "删除",
    },
  },
};
