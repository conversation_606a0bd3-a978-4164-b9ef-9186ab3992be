export default {
  "markdown": {
    "title": "Markdown Editor",
    "switchToMarkdown": "Switch to Markdown",
    "switchToPlainText": "Switch to Plain Text",
    "permissionRequired": "Admin privileges or a valid API key are required to create shares, please",
    "loginOrAuth": "login to admin panel or configure API key",
    "form": {
      "remark": "Remark",
      "remarkPlaceholder": "Add a remark for this share (optional)",
      "customLink": "Custom Link Suffix",
      "customLinkPlaceholder": "Custom link suffix (optional)",
      "password": "Access Password",
      "passwordPlaceholder": "Set access password (optional)",
      "expiryTime": "Expiry Time",
      "expiryNever": "Never expires",
      "expiryHour": "hours",
      "expiryDay": "days",
      "expiryWeek": "weeks",
      "expiryMonth": "months",
      "maxViews": "Max Views",
      "maxViewsPlaceholder": "Limit view count (0 for unlimited)",
      "createShare": "Create Share",
      "creating": "Creating..."
    },
    "shareLink": "Share Link:",
    "copyLink": "Copy Link",
    "copyRawLink": "Copy Raw Text Link",
    "showQRCode": "Show QR Code",
    "linkCopied": "Link copied to clipboard",
    "rawLinkCopied": "Raw text link copied to clipboard",
    "copyFailed": "Copy failed, please copy manually",
    "linkExpireIn": "Link will be hidden in {seconds} seconds",
    "qrCodeTitle": "Share QR Code",
    "qrCodeGenerating": "Generating...",
    "qrCodeScanToAccess": "Scan QR code to access shared content",
    "downloadQRCode": "Download QR Code",
    "qrCodeDownloaded": "QR code downloaded",
    "copyFormats": "Copy Formats",
    "copyAsMarkdown": "Copy as Markdown",
    "copyAsHTML": "Copy as HTML",
    "copyAsPlainText": "Copy as Plain Text",
    "exportAsWord": "Export as Word Document",
    "exportAsPng": "Export as PNG Image",
    "exportDocumentTitle": "Markdown Export Document",
    "markdownCopied": "Copied as Markdown format",
    "htmlCopied": "Copied as HTML format",
    "plainTextCopied": "Copied as plain text format",
    "editorPlaceholder": "Enter Markdown content here...",
    "plainTextPlaceholder": "Enter plain text content here...",
    "toolbar": {
      "importFile": "Import File",
      "clearContent": "Clear Content",
      "copyFormats": "Copy Formats"
    },
    "messages": {
      "contentEmpty": "Content cannot be empty",
      "creating": "Creating share...",
      "createSuccess": "Share created successfully!",
      "createFailed": "Save failed",
      "linkOccupied": "Link suffix is already taken, please use another one",
      "contentTooLarge": "Content too large, please reduce content length",
      "unknownError": "Unknown error",
      "editorNotReady": "Editor not ready",
      "autoSaveFailed": "Auto-save failed",
      "restoreContentFailed": "Failed to restore content",
      "qrCodeGenerateFailed": "Failed to generate QR code",
      "confirmClearContent": "Are you sure you want to clear all content?",
      "generatingWord": "Generating Word document...",
      "wordExported": "Word document generated and downloaded",
      "wordExportFailed": "Export failed, please try again later",
      "exportingPng": "Exporting PNG image...",
      "pngExported": "PNG image exported and downloaded",
      "pngExportFailed": "PNG export failed, please try again later",
      "corsImageError": "Due to CORS restrictions, some images may not display correctly in the exported image"
    },
    "validation": {
      "slugInvalid": "Link suffix can only contain letters, numbers, hyphens and underscores",
      "slugTooLong": "Link suffix cannot exceed 50 characters",
      "slugReserved": "This link suffix is reserved by the system, please use another one"
    }
  }
};