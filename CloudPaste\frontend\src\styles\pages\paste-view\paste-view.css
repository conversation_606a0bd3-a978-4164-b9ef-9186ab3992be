/* 编辑器容器样式 */
.paste-view-editor {
  width: 100%;
}

.editor-wrapper {
  width: 100%;
}

/* 编辑器容器适应屏幕宽度，添加边框和圆角 */
:deep(#vditor-editor) {
  width: 100%;
  border: 1px solid v-bind('props.darkMode ? "#4B5563" : "#E5E7EB"');
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  overflow: hidden;
}

/* 纯文本编辑区样式 */
textarea.w-full {
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  line-height: 1.5;
  tab-size: 4;
  -moz-tab-size: 4;
}

/* 工具栏样式调整，适应明暗主题 */
:deep(.vditor-toolbar) {
  background-color: v-bind('props.darkMode ? "#374151" : "#F9FAFB"');
  border-bottom: 1px solid v-bind('props.darkMode ? "#4B5563" : "#E5E7EB"');
}

:deep(.vditor-toolbar__item) {
  color: v-bind('props.darkMode ? "#E5E7EB" : "#4B5563"');
}

:deep(.vditor-toolbar__item--current) {
  background-color: v-bind('props.darkMode ? "#1F2937" : "#F3F4F6"');
}

:deep(.vditor-toolbar__item:hover) {
  background-color: v-bind('props.darkMode ? "#4B5563" : "#E5E7EB"');
}

/* 表单和按钮通用样式 */
.form-input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-width: 1px;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

/* 主按钮样式 */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.375rem;
  background-color: v-bind('props.darkMode ? "#3b82f6" : "#2563eb"');
  color: white;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: v-bind('props.darkMode ? "#2563eb" : "#1d4ed8"');
}

.btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 编辑器样式定制 */
:deep(.vditor) {
  border-radius: 0.375rem;
  /* 移除整体过渡效果，避免拖动时的延迟感 */
  transition: border-color 0.2s, background-color 0.2s;
}

/* 确保编辑器图表有固定样式 */
:deep(.diagram-fixed-theme) {
  background-color: white !important; /* 强制使用白色背景 */
  color: #333 !important; /* 强制使用深色文本 */
  filter: none !important; /* 移除任何可能的过滤器效果 */
}

/* 编辑器高度调整拖动区域样式 */
:deep(.vditor-resize) {
  padding: 3px 0;
  cursor: row-resize;
  user-select: none;
  position: absolute;
  width: 100%;
  z-index: 20; /* 提高z-index确保在最上层 */
  transition: none; /* 移除过渡动画，避免拖动时的延迟感 */
}

:deep(.vditor-resize > div) {
  height: 3px;
  background-color: v-bind('props.darkMode ? "#3f3f3f" : "#e5e7eb"');
  border-radius: 3px;
  transition: background-color 0.15s; /* 只保留背景色的过渡效果 */
}

:deep(.vditor-resize:hover > div) {
  background-color: v-bind('props.darkMode ? "#007acc" : "#d1d5db"');
}

:deep(.vditor-resize:active > div) {
  background-color: v-bind('props.darkMode ? "#2563eb" : "#3b82f6"');
}

/* 移动端响应式优化 */
@media (max-width: 640px) {
  /* 移动设备下编辑器工具栏精简 */
  :deep(.vditor-toolbar) {
    overflow-x: auto;
    white-space: nowrap;
    padding: 0.25rem;
  }
}

/* 编辑器样式定制 */
:deep(.vditor) {
  border-radius: 0.375rem;
  /* 移除整体过渡效果，避免拖动时的延迟感 */
  transition: border-color 0.2s, background-color 0.2s;
}

/* 确保编辑器图表有固定样式 */
:deep(.diagram-fixed-theme) {
  background-color: white !important; /* 强制使用白色背景 */
  color: #333 !important; /* 强制使用深色文本 */
  filter: none !important; /* 移除任何可能的过滤器效果 */
}

/* 确保mermaid图表使用固定样式 */
:deep(.language-mermaid) {
  background-color: white !important;
  padding: 10px !important;
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* 流程图样式固定 */
:deep(.language-flow) {
  background-color: white !important;
  padding: 10px !important;
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
}

/* PlantUML图表样式固定 */
:deep(.language-plantuml) {
  background-color: white !important;
  padding: 10px !important;
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
}

/* 甘特图样式固定 */
:deep(.language-gantt) {
  background-color: white !important;
  padding: 10px !important;
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
}

/* 图表内的SVG元素样式固定 */
:deep(.diagram-fixed-theme svg) {
  background-color: white !important;
}

/* 添加多级列表样式支持 */
/* 有序列表样式 */
:deep(.vditor-reset ol) {
  list-style-type: decimal;
  padding-left: 2em;
}

:deep(.vditor-reset ol ol) {
  list-style-type: decimal;
}

:deep(.vditor-reset ol ol ol) {
  list-style-type: decimal;
}

/* 无序列表样式 */
:deep(.vditor-reset ul) {
  list-style-type: disc;
  padding-left: 2em;
}

:deep(.vditor-reset ul ul) {
  list-style-type: circle;
}

:deep(.vditor-reset ul ul ul) {
  list-style-type: square;
}

/* 预览模式列表样式 */
:deep(.vditor-preview ol) {
  list-style-type: decimal;
  padding-left: 2em;
}

:deep(.vditor-preview ol ol) {
  list-style-type: decimal;
}

:deep(.vditor-preview ol ol ol) {
  list-style-type: decimal;
}

:deep(.vditor-preview ul) {
  list-style-type: disc;
  padding-left: 2em;
}

:deep(.vditor-preview ul ul) {
  list-style-type: circle;
}

:deep(.vditor-preview ul ul ul) {
  list-style-type: square;
}

/* 确保即时渲染模式的列表也正确显示 */
:deep(.vditor-ir ol) {
  list-style-type: decimal;
  padding-left: 2em;
}

:deep(.vditor-ir ol ol) {
  list-style-type: decimal;
}

:deep(.vditor-ir ol ol ol) {
  list-style-type: decimal;
}

:deep(.vditor-ir ul) {
  list-style-type: disc;
  padding-left: 2em;
}

:deep(.vditor-ir ul ul) {
  list-style-type: circle;
}

:deep(.vditor-ir ul ul ul) {
  list-style-type: square;
}

/* 制表符样式支持 */
:deep(.vditor-reset) {
  tab-size: 4;
  -moz-tab-size: 4;
}

:deep(.vditor-ir) {
  tab-size: 4;
  -moz-tab-size: 4;
}

:deep(.vditor-sv) {
  tab-size: 4;
  -moz-tab-size: 4;
}

:deep(.vditor-wysiwyg) {
  tab-size: 4;
  -moz-tab-size: 4;
}

/* 复制格式菜单样式 */
#copyFormatMenu {
  min-width: 180px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  transform-origin: top left;
}

#copyFormatMenu div {
  transition: background-color 0.15s ease-in-out;
}

/* 通知提示动画 */
@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  10% {
    opacity: 1;
    transform: translateY(0);
  }
  90% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

.notification-toast {
  animation: fadeInOut 3s ease-in-out forwards;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 添加多级列表样式支持 */
/* 有序列表样式 */
