export default {
  gallery: {
    // Toolbar options
    columns: "Columns",
    spacing: "Spacing",
    horizontalSpacing: "Horizontal Spacing",
    verticalSpacing: "Vertical Spacing",
    imageOnlyMode: "Images Only",
    sort: "Sort",
    settings: "Settings",
    reset: "Reset",
    resetSettings: "Reset all gallery settings to default values",
    alreadyDefault: "Already using default settings",

    // Sort options
    sortByName: "Sort by Name",
    sortBySize: "Sort by Size",
    sortByDate: "Sort by Date",
    sortByType: "Sort by Type",

    // Option values
    auto: "Auto",
    columnsUnit: " cols",
    small: "Small",
    medium: "Medium",
    large: "Large",
    tight: "Tight",
    loose: "Loose",

    // Content statistics
    imagesCount: " images",
    foldersCount: " folders",
    otherFilesCount: " other files",
    noImages: "No images",

    // Empty state
    noImagesTitle: "No images in this directory",
    noImagesDescription: "Gallery mode is designed for browsing image files. Switch to other view modes to see all files.",
    emptyFolder: "Empty folder",

    // Action buttons
    loadMore: "Load More Images",

    // Loading states
    loading: "Loading...",
    waitingToLoad: "Waiting to load",
    loadingMore: "Loading more...",
    loadError: "Load failed",

    // View mode name
    viewModeName: "Gallery View",
  },
};
