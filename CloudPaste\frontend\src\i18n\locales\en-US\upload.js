export default {
  file: {
    uploadPageTitle: "File Upload",
    permissionRequired: "Admin privileges or a valid API key are required to upload files, please",
    loginOrAuth: "login to admin panel or configure API key",
    uploadTabs: {
      fileUpload: "File Upload",
      urlUpload: "URL Direct Link Upload",
    },
    dragDropTitle: "Drag files here or click to select",
    dragDropSubtitle: "Multiple file upload supported",
    dragDropHint: "Maximum file size: {size}",
    selectFiles: "Select Files",
    drag: "Drag files here",
    select: "Drag files here or click to select",
    maxSizeExceeded: "Maximum file size: {size}",
    multipleFilesSupported: "Multiple file upload supported",
    selectedFiles: "{count} files selected",
    clearAll: "Clear All",
    pending: "Pending",
    success: "Success",
    error: "Error",
    retry: "Retry",
    clearSelected: "Remove",
    cancelUpload: "Cancel Upload",
    fileName: "File Name",
    fileSize: "Size",
    fileType: "Type",
    password: "Password",
    createdAt: "Created At",
    actions: "Actions",
    remainingViewsLabel: "Remaining Views",
    unlimited: "Unlimited",
    usedUp: "Used Up",
    unknownSize: "Unknown Size",
    form: {
      s3Config: "Storage Config",
      selectS3Config: "Select Storage Config",
      customSlug: "Custom Link",
      customSlugPlaceholder: "Custom file access link (optional)",
      path: "Storage Path",
      pathPlaceholder: "File storage path (optional)",
      remark: "Remark",
      remarkPlaceholder: "Add a remark for this file (optional)",
      password: "Access Password",
      passwordPlaceholder: "Set access password (optional)",
      expiryTime: "Expiry Time",
      expiryNever: "Never expires",
      expiryHour: "hours",
      expiryDay: "days",
      expiryWeek: "weeks",
      expiryMonth: "months",
      maxViews: "Max Views",
      maxViewsPlaceholder: "Limit view count (0 for unlimited)",
      uploadButton: "Start Upload",
      uploading: "Uploading...",
      cancelUpload: "Cancel Upload",
    },
    storage: "Storage Config",
    selectStorage: "Select Storage Config",
    noStorage: "No storage config available",
    path: "Storage Path",
    pathPlaceholder: "File storage path (optional)",
    shareSettings: "Share Settings",
    remark: "Remark",
    remarkPlaceholder: "Add a remark for this file (optional)",
    customLink: "Custom Link",
    customLinkPlaceholder: "Custom file access link (optional)",
    passwordProtection: "Password Protection",
    passwordPlaceholder: "Set access password (optional)",
    expireTime: "Expiry Time",
    maxViews: "Max Views",
    maxViewsPlaceholder: "Limit view count (0 for unlimited)",
    onlyAllowedChars: "Only letters, numbers, hyphens and underscores allowed",
    expireOptions: {
      hour1: "Expires in 1 hour",
      day1: "Expires in 1 day",
      day7: "Expires in 7 days",
      day30: "Expires in 30 days",
      never: "Never expires",
    },
    upload: "Start Upload",
    loading: "Loading...",
    cancel: "Cancel",
    urlUpload: {
      urlInput: "File URL",
      urlInputPlaceholder: "Enter the URL of the file to upload",
      analyzeUrl: "Analyze URL",
      analyzing: "Analyzing...",
      customFilename: "Custom Filename",
      customFilenamePlaceholder: "Custom filename (optional)",
      filePreview: "File Preview",
      uploadFromUrl: "Upload from URL",
      urlUpload: "URL Upload",
      urlAnalysisComplete: "URL analysis complete",
      retryAnalysis: "Retry Analysis",
    },
    enterUrl: "Enter file URL",
    supportedUrlTypes: "Support HTTP and HTTPS links",
    urlPlaceholder: "Enter the URL of the file to upload",
    analyze: "Analyze",
    analyzing: "Analyzing...",
    urlFileInfo: "File Information",
    clear: "Clear",
    customFileName: "Custom Filename",
    customFilename: "Custom filename (optional)",
    uploadMethod: "Upload Method",
    presignedUpload: "Presigned Upload",
    multipartUpload: "Multipart Upload",
    presignedUploadDesc: "Pre-signed URL direct upload to storage",
    multipartUploadDesc: "Direct upload of shards to storage",
    starting: "Starting...",
    downloading: "Downloading...",
    downloadingProxy: "Proxy downloading...",
    preparing: "Preparing...",
    initializing: "Initializing...",
    uploading: "Uploading...",
    finalizing: "Finalizing...",
    completed: "Completed",
    cancelled: "Cancelled",
    recentUploads: "Recent Uploads",
    showingRecent: "Showing recent 3 records",
    noFiles: "No files",
    noFilesUploaded: "No files uploaded",
    uploadToShow: "Upload files to display here",
    open: "Open",
    delete: "Delete",
    qrCode: "QR Code",
    encrypted: "Encrypted",
    noPassword: "No Password",
    fileQrCode: "File QR Code",
    deleting: "Deleting...",
    confirmDeleteBtn: "Confirm Delete",
    deletedSuccess: "File deleted successfully",
    qrCodeDownloadSuccess: "QR code downloaded successfully",
    noValidLink: "No valid link",
    cannotGetProxyLink: "Cannot get proxy link",
    copyPermanentLinkFailed: "Failed to copy permanent link",
    getPasswordFromSessionError: "Failed to get password from session storage",
    copyLink: "Copy Link",
    copyDirectLink: "Copy Direct Link",
    downloadFile: "Download File",
    deleteFile: "Delete File",
    showQRCode: "Show QR Code",
    downloadQrCode: "Download QR Code",
    uploadSuccessful: "File uploaded successfully",
    urlUploadSuccess: "URL file uploaded successfully",
    multipleUploadsSuccessful: "Successfully uploaded {count} files",
    retrySuccessful: "Retry upload successful",
    allSlugConflicts: "All file link suffixes are already taken, please use different ones",
    allPermissionErrors: "No permission to use this storage configuration",
    allUploadsFailed: "All file uploads failed",
    someSlugConflicts: "{count} file link suffixes are already taken",
    someUploadsFailed: "{count} file uploads failed",
    singleFileCancelMessage: "File upload cancelled",
    insufficientStorageDetailed: "Insufficient storage: file size({fileSize}) exceeds remaining space({remainingSpace}), total capacity limit is {totalCapacity}",
    linkCopied: "Link copied to clipboard",
    directLinkCopied: "Direct link copied to clipboard",
    copyFailed: "Copy failed, please copy manually",
    qrCodeDownloaded: "QR code downloaded",
    messages: {
      noS3Config: "Please select a storage configuration",
      noFilesSelected: "Please select files to upload",
      fileTooLarge: "File size exceeds limit",
      uploadFailed: "Upload failed",
      uploadCancelled: "Upload cancelled",
      deleteFailed: "Delete failed",
      getFileDetailFailed: "Failed to get file details",
      cannotGetDirectLink: "Cannot get direct link, please try again later",
      invalidUrl: "Please enter a valid URL",
      urlAnalysisFailed: "URL analysis failed",
      negativeMaxViews: "Maximum views cannot be negative",
      getPresignedUrlFailed: "Failed to get presigned URL",
      slugInvalid: "Custom link can only contain letters, numbers, hyphens and underscores",
      slugTooLong: "Custom link cannot exceed 50 characters",
      slugReserved: "This link is reserved by the system, please use another one",
      slugConflict: "Link suffix is already taken, please use another one",
      permissionError: "No permission to use this storage configuration",
      initMultipartUploadFailed: "Failed to initialize multipart upload",
      networkError: "Network error, please check your connection",
      serverError: "Server error, please try again later",
      unknownError: "Unknown error",
    },
    confirmDelete: "Confirm Delete",
    confirmDeleteMessage: "Are you sure you want to delete this file? This action cannot be undone.",
    confirm: "Confirm",
    qrCodeTitle: "File QR Code",
    qrCodeGenerating: "Generating...",
    qrCodeScanToAccess: "Scan QR code to access file",
    uploadProgress: "Upload Progress",
    uploadSpeed: "Upload Speed",
    uploadStage: {
      starting: "Preparing upload...",
      initializing: "Initializing...",
      uploading: "Uploading...",
      processing: "Processing...",
      completing: "Completing...",
      completed: "Upload completed",
    },
  },
};
